#!/bin/bash

# VPN Connection Testing Script for Android Emulator
# Usage: ./test_vpn_connection.sh

echo "🔍 VPN Connection Testing Script"
echo "================================"

ADB_PATH="/Users/<USER>/Library/Android/sdk/platform-tools/adb"

echo ""
echo "📱 Step 1: Check Current Network Interfaces"
echo "-------------------------------------------"
$ADB_PATH shell ip addr show | grep -E "(tun|ppp|vpn)" || echo "No VPN interfaces found"

echo ""
echo "📊 Step 2: Check Current IP Configuration"
echo "----------------------------------------"
echo "Current network interfaces:"
$ADB_PATH shell ip route show

echo ""
echo "🌐 Step 3: Test Internet Connectivity"
echo "------------------------------------"
echo "Testing connectivity to Google DNS..."
$ADB_PATH shell ping -c 3 *******

echo ""
echo "📋 Step 4: Check Running Processes"
echo "---------------------------------"
echo "Looking for VPN-related processes..."
$ADB_PATH shell ps | grep -i vpn || echo "No VPN processes found"

echo ""
echo "🔧 Step 5: Check Network Statistics"
echo "----------------------------------"
$ADB_PATH shell cat /proc/net/dev | head -10

echo ""
echo "📝 Instructions for Manual Testing:"
echo "===================================="
echo "1. Run this script BEFORE connecting VPN"
echo "2. Connect VPN through your app"
echo "3. Run this script AFTER connecting VPN"
echo "4. Compare the outputs to see changes"
echo ""
echo "🌍 Browser Testing:"
echo "- Open Chrome in emulator"
echo "- Visit: whatismyipaddress.com"
echo "- Note IP before and after VPN connection"
echo ""
echo "✅ VPN is working if:"
echo "- New 'tun' interface appears"
echo "- IP address changes in browser"
echo "- Location changes to VPN server country"
