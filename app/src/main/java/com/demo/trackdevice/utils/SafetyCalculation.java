package com.demo.trackdevice.utils;

import android.content.Context;

import com.demo.trackdevice.vpn.VpnManager;

public class SafetyCalculation {
    AppPreferences appPreferences;

    public int SafetyCalculationCount(Context context) {
        AppPreferences appPreferences2 = new AppPreferences(context);
        this.appPreferences = appPreferences2;

        // Recalculated scores to accommodate VPN (total = 100%)
        int i = appPreferences2.getCameraPermissionTrusted() ? 10 : 0;  // Reduced from 12 to 10
        if (this.appPreferences.getLocationPermissionTrusted()) {
            i += 10;  // Reduced from 12 to 10
        }
        if (this.appPreferences.getStoragePermissionTrusted()) {
            i += 10;  // Reduced from 12 to 10
        }
        if (this.appPreferences.getContactsPermissionTrusted()) {
            i += 10;  // Reduced from 12 to 10
        }
        if (this.appPreferences.getMicPermissionTrusted()) {
            i += 10;  // Reduced from 12 to 10
        }
        if (this.appPreferences.getNetworkPermissionTrusted()) {
            i += 10;  // Reduced from 12 to 10
        }
        if (this.appPreferences.getNetworkSecurityTrusted()) {
            i += 12;  // Reduced from 14 to 12
        }
        if (this.appPreferences.getDeviceInfoTrusted()) {
            i += 12;  // Reduced from 14 to 12
        }

        // Add VPN protection score (16 points for VPN connection)
        VpnManager vpnManager = new VpnManager(context);
        if (vpnManager.isVpnConnected()) {
            i += 16;
        }

        return i;
    }
}
