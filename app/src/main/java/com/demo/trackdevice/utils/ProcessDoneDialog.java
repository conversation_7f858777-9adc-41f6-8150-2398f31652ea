package com.demo.trackdevice.utils;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.demo.trackdevice.R;

public class ProcessDoneDialog {


    public static void show(Context context, String str) {
        final Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(1);
        dialog.setCancelable(false);
        dialog.setContentView(R.layout.dialog_process_done);
        ((TextView) dialog.findViewById(R.id.text)).setText(str);
        ((TextView) dialog.findViewById(R.id.btnCompleted)).setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }
}
