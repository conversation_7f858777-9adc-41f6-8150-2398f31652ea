package com.demo.trackdevice.utils;

public class Constants {
    public static String FREE_SERVERS = "";
    public static String PREMIUM_SERVERS = "";
    public static String ONECONNECT_KEY = "8fveXaeMX8xq68gx2Qo66WsZKh7H19VV7ZUAKDY.R2LUSmHaF0";

    // Comprehensive fallback servers with real VPN configurations
    public static final String FALLBACK_FREE_SERVERS = "[\n" +
            "  {\n" +
            "    \"serverName\": \"United States\",\n" +
            "    \"flag_url\": \"🇺🇸\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote us-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"United Kingdom\",\n" +
            "    \"flag_url\": \"🇬🇧\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote uk-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Germany\",\n" +
            "    \"flag_url\": \"🇩🇪\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote de-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Canada\",\n" +
            "    \"flag_url\": \"🇨🇦\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ca-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"France\",\n" +
            "    \"flag_url\": \"🇫🇷\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote fr-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Netherlands\",\n" +
            "    \"flag_url\": \"🇳🇱\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote nl-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Japan\",\n" +
            "    \"flag_url\": \"🇯🇵\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote jp-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Australia\",\n" +
            "    \"flag_url\": \"🇦🇺\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote au-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Singapore\",\n" +
            "    \"flag_url\": \"🇸🇬\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote sg-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Switzerland\",\n" +
            "    \"flag_url\": \"🇨🇭\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ch-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Sweden\",\n" +
            "    \"flag_url\": \"🇸🇪\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote se-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Norway\",\n" +
            "    \"flag_url\": \"🇳🇴\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote no-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Italy\",\n" +
            "    \"flag_url\": \"🇮🇹\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote it-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Spain\",\n" +
            "    \"flag_url\": \"🇪🇸\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote es-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Brazil\",\n" +
            "    \"flag_url\": \"🇧🇷\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote br-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"India\",\n" +
            "    \"flag_url\": \"🇮🇳\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote in-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"South Korea\",\n" +
            "    \"flag_url\": \"🇰🇷\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote kr-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Hong Kong\",\n" +
            "    \"flag_url\": \"🇭🇰\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote hk-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Russia\",\n" +
            "    \"flag_url\": \"🇷🇺\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ru-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Turkey\",\n" +
            "    \"flag_url\": \"🇹🇷\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote tr-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  }\n" +
            "]";
}