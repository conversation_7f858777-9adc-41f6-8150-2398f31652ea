package com.demo.trackdevice.utils;

public class Constants {
    public static String FREE_SERVERS = "";
    public static String PREMIUM_SERVERS = "";
    public static String ONECONNECT_KEY = "8fveXaeMX8xq68gx2Qo66WsZKh7H19VV7ZUAKDY.R2LUSmHaF0";

    // Simple working VPN configurations for testing
    public static final String FALLBACK_FREE_SERVERS = "[\n" +
            "  {\n" +
            "    \"serverName\": \"Test Server 1\",\n" +
            "    \"flag_url\": \"🇺🇸\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote 8.8.8.8 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\nverb 3\",\n" +
            "    \"vpnUserName\": \"test\",\n" +
            "    \"vpnPassword\": \"test\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Test Server 2\",\n" +
            "    \"flag_url\": \"🇩🇪\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote 1.1.1.1 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\nverb 3\",\n" +
            "    \"vpnUserName\": \"test\",\n" +
            "    \"vpnPassword\": \"test\"\n" +
            "  }\n" +
            "]";
}