package com.demo.trackdevice.utils;

public class Constants {
    public static String FREE_SERVERS = "";
    public static String PREMIUM_SERVERS = "";
    public static String ONECONNECT_KEY = "8fveXaeMX8xq68gx2Qo66WsZKh7H19VV7ZUAKDY.R2LUSmHaF0";

    // Fallback servers in case OneConnect API fails
    public static final String FALLBACK_FREE_SERVERS = "[\n" +
            "  {\n" +
            "    \"serverName\": \"United States\",\n" +
            "    \"flag_url\": \"🇺🇸\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote us-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Germany\",\n" +
            "    \"flag_url\": \"🇩🇪\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote de-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"serverName\": \"Canada\",\n" +
            "    \"flag_url\": \"🇨🇦\",\n" +
            "    \"ovpnConfiguration\": \"client\\ndev tun\\nproto udp\\nremote ca-free.vpnbook.com 1194\\nresolv-retry infinite\\nnobind\\npersist-key\\npersist-tun\\nauth-user-pass\\ncomp-lzo\\nverb 3\\nauth SHA1\\ncipher BF-CBC\",\n" +
            "    \"vpnUserName\": \"vpnbook\",\n" +
            "    \"vpnPassword\": \"e8j64wq\"\n" +
            "  }\n" +
            "]";
}