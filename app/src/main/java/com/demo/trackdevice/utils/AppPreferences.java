package com.demo.trackdevice.utils;

import android.content.Context;
import android.content.SharedPreferences;

public class AppPreferences {
    private static final String PREF_NAME = "MyPrefs";
    private static final String PREF_TRUSTED_CAMERA_PERMISSION = "CAMERA_PERMISSION";
    private static final String PREF_TRUSTED_CONTACT_PERMISSION = "CONTACT_PERMISSION";
    private static final String PREF_TRUSTED_DEVICE_INFO = "DEVICE_INFO";
    private static final String PREF_TRUSTED_LOCATION_PERMISSION = "LOCATION_PERMISSION";
    private static final String PREF_TRUSTED_MIC_PERMISSION = "MIC_PERMISSION";
    private static final String PREF_TRUSTED_NETWORK_PERMISSION = "NETWORK_PERMISSION";
    private static final String PREF_TRUSTED_NETWORK_SECURITY = "NETWORK_SECURITY";
    private static final String PREF_TRUSTED_STORAGE_PERMISSION = "STORAGE_PERMISSION";
    public static boolean uninstallCall = false;
    private SharedPreferences sharedPreferences;

    public AppPreferences(Context context) {
        this.sharedPreferences = context.getSharedPreferences(PREF_NAME, 0);
    }

    public boolean getCameraPermissionTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_CAMERA_PERMISSION, false);
    }

    public void setCameraPermissionTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_CAMERA_PERMISSION, z).apply();
    }

    public boolean getLocationPermissionTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_LOCATION_PERMISSION, false);
    }

    public void setLocationPermissionTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_LOCATION_PERMISSION, z).apply();
    }

    public boolean getStoragePermissionTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_STORAGE_PERMISSION, false);
    }

    public void setStoragePermissionTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_STORAGE_PERMISSION, z).apply();
    }

    public boolean getContactsPermissionTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_CONTACT_PERMISSION, false);
    }

    public void setContactsPermissionTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_CONTACT_PERMISSION, z).apply();
    }

    public boolean getMicPermissionTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_MIC_PERMISSION, false);
    }

    public void setMicPermissionTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_MIC_PERMISSION, z).apply();
    }

    public boolean getNetworkPermissionTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_NETWORK_PERMISSION, false);
    }

    public void setNetworkPermissionTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_NETWORK_PERMISSION, z).apply();
    }

    public boolean getNetworkSecurityTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_NETWORK_SECURITY, false);
    }

    public void setNetworkSecurityTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_NETWORK_SECURITY, z).apply();
    }

    public boolean getDeviceInfoTrusted() {
        return this.sharedPreferences.getBoolean(PREF_TRUSTED_DEVICE_INFO, false);
    }

    public void setDeviceInfoTrusted(boolean z) {
        this.sharedPreferences.edit().putBoolean(PREF_TRUSTED_DEVICE_INFO, z).apply();
    }
}
