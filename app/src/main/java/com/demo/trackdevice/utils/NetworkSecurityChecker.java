package com.demo.trackdevice.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.wifi.WifiManager;
import java.io.IOException;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.cert.Certificate;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.X509Certificate;
import javax.net.ssl.HttpsURLConnection;

public class NetworkSecurityChecker {
    private final Context context;

    public NetworkSecurityChecker(Context context2) {
        this.context = context2;
    }

    public String checkNetworkSecurity() {
        StringBuilder sb = new StringBuilder();
        ConnectivityManager connectivityManager = (ConnectivityManager) this.context.getSystemService("connectivity");
        Network activeNetwork = connectivityManager.getActiveNetwork();
        NetworkCapabilities networkCapabilities = activeNetwork != null ? connectivityManager.getNetworkCapabilities(activeNetwork) : null;
        if (networkCapabilities == null) {
            sb.append("OFF");
        } else if (networkCapabilities.hasTransport(1)) {
            ((WifiManager) this.context.getSystemService("wifi")).getConnectionInfo().getSSID();
            sb.append("WIFI");
        } else if (networkCapabilities.hasTransport(0)) {
            sb.append("MOBILE");
        }
        return sb.toString();
    }

    public String checkSSLStatus() {
        String str = "";
        try {
            HttpsURLConnection httpsURLConnection = (HttpsURLConnection) new URL("https://www.google.co.in/").openConnection();
            httpsURLConnection.connect();
            for (Certificate certificate : httpsURLConnection.getServerCertificates()) {
                if (certificate instanceof X509Certificate) {
                    ((X509Certificate) certificate).checkValidity();
                    str = "Valid";
                }
            }
            httpsURLConnection.disconnect();
            return str;
        } catch (IOException unused) {
        } catch (CertificateNotYetValidException e) {
            throw new RuntimeException(e);
        } catch (CertificateExpiredException e) {
            throw new RuntimeException(e);
        }
        return str;
    }

    public String checkDNSStatus() {
        StringBuilder sb = new StringBuilder();
        if (isDNSValid("www.example.com")) {
            sb.append("Valid");
        } else {
            sb.append("Invalid");
        }
        return sb.toString();
    }

    private boolean isDNSValid(String str) {
        try {
            InetAddress[] allByName = InetAddress.getAllByName(str);
            if (allByName == null || allByName.length <= 0) {
                return false;
            }
            return true;
        } catch (Exception unused) {
            return false;
        }
    }
}
