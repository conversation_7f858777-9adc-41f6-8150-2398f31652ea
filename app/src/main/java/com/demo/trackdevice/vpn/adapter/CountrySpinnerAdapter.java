package com.demo.trackdevice.vpn.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.demo.trackdevice.R;
import com.demo.trackdevice.vpn.model.Countries;

import java.util.List;

public class CountrySpinnerAdapter extends BaseAdapter {
    
    private Context context;
    private List<Countries> countries;
    private LayoutInflater inflater;
    
    public CountrySpinnerAdapter(Context context, List<Countries> countries) {
        this.context = context;
        this.countries = countries;
        this.inflater = LayoutInflater.from(context);
    }
    
    @Override
    public int getCount() {
        return countries.size();
    }
    
    @Override
    public Object getItem(int position) {
        return countries.get(position);
    }
    
    @Override
    public long getItemId(int position) {
        return position;
    }
    
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent);
    }
    
    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent);
    }
    
    private View createView(int position, View convertView, ViewGroup parent) {
        View view = convertView;
        if (view == null) {
            view = inflater.inflate(R.layout.spinner_country_item, parent, false);
        }
        
        Countries country = countries.get(position);
        
        TextView tvFlag = view.findViewById(R.id.tv_flag);
        TextView tvCountry = view.findViewById(R.id.tv_country);
        
        tvFlag.setText(country.getFlagUrl());
        tvCountry.setText(country.getCountry());
        
        return view;
    }
}
