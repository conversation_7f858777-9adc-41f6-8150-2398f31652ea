package com.demo.trackdevice.vpn.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.demo.trackdevice.R;
import com.demo.trackdevice.vpn.model.Countries;

import java.util.List;

public class ServerAdapter extends RecyclerView.Adapter<ServerAdapter.ServerViewHolder> {
    
    private List<Countries> serverList;
    private OnServerClickListener listener;
    
    public interface OnServerClickListener {
        void onServerClick(Countries server);
    }
    
    public ServerAdapter(List<Countries> serverList, OnServerClickListener listener) {
        this.serverList = serverList;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ServerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_server, parent, false);
        return new ServerViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ServerViewHolder holder, int position) {
        Countries server = serverList.get(position);
        holder.bind(server);
    }
    
    @Override
    public int getItemCount() {
        return serverList.size();
    }
    
    class ServerViewHolder extends RecyclerView.ViewHolder {
        private TextView tvCountryName;
        private TextView tvCountryFlag;
        
        public ServerViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCountryName = itemView.findViewById(R.id.tv_country_name);
            tvCountryFlag = itemView.findViewById(R.id.tv_country_flag);
            
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onServerClick(serverList.get(getAdapterPosition()));
                }
            });
        }
        
        public void bind(Countries server) {
            tvCountryName.setText(server.getCountry());
            tvCountryFlag.setText(server.getFlagUrl());
        }
    }
}
