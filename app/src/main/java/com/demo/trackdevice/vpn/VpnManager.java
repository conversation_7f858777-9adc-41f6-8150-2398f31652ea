package com.demo.trackdevice.vpn;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.TrafficStats;
import android.net.VpnService;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.demo.trackdevice.vpn.model.Countries;
import com.demo.trackdevice.vpn.speed.Speed;
import com.demo.trackdevice.vpn.utils.ActiveServer;
import com.demo.trackdevice.utils.Constants;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.io.IOException;

import top.oneconnectapi.app.OpenVpnApi;
import top.oneconnectapi.app.core.OpenVPNThread;
import top.oneconnectapi.app.api.OneConnect;

public class VpnManager {
    private static final String TAG = "VpnManager";
    private static final String PREFS_NAME = "vpn_prefs";
    private static final String KEY_VPN_CONNECTED = "vpn_connected";
    private static final String KEY_SELECTED_SERVER = "selected_server";
    private static final String VPN_BROADCAST_ACTION = "vpnstatus";

    private Context context;
    private SharedPreferences prefs;
    private VpnConnectionListener listener;
    private OpenVPNThread vpnThread;
    private Speed speed;
    private Handler speedHandler;
    private BroadcastReceiver vpnStatusReceiver;
    private boolean serversInitialized = false;

    // Speed monitoring variables
    private long mLastRxBytes = 0;
    private long mLastTxBytes = 0;
    private long mLastTime = 0;

    // Connection state
    private String currentStatus = "DISCONNECTED";
    private Countries selectedServer;

    public interface VpnConnectionListener {
        void onConnected();
        void onDisconnected();
        void onConnecting();
        void onError(String error);
        void onStatusChanged(String status);
        void onSpeedUpdate(String downloadSpeed, String uploadSpeed);
        void onConnectionStats(String duration, String byteIn, String byteOut);
    }

    public VpnManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.speed = new Speed(context);
        this.speedHandler = new Handler(Looper.getMainLooper());
        initializeVpnStatusReceiver();
        initializeServers();
        loadSavedServer();
    }

    private void initializeServers() {
        if (!serversInitialized) {
            new Thread(() -> {
                try {
                    OneConnect oneConnect = new OneConnect();
                    oneConnect.initialize(context, Constants.ONECONNECT_KEY);
                    try {
                        Constants.FREE_SERVERS = oneConnect.fetch(true);
                        Constants.PREMIUM_SERVERS = oneConnect.fetch(false);
                        serversInitialized = true;
                        Log.d(TAG, "OneConnect servers loaded successfully");
                    } catch (IOException e) {
                        Log.w(TAG, "Failed to fetch OneConnect servers, using fallback: " + e.getMessage());
                        Constants.FREE_SERVERS = Constants.FALLBACK_FREE_SERVERS;
                        serversInitialized = true;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error initializing OneConnect: " + e.getMessage());
                    Constants.FREE_SERVERS = Constants.FALLBACK_FREE_SERVERS;
                    serversInitialized = true;
                }
            }).start();
        }
    }
    
    public void setConnectionListener(VpnConnectionListener listener) {
        this.listener = listener;
    }

    private void initializeVpnStatusReceiver() {
        vpnStatusReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                try {
                    String status = intent.getStringExtra("state");
                    String duration = intent.getStringExtra("duration");
                    String lastPacketReceive = intent.getStringExtra("lastPacketReceive");
                    String byteIn = intent.getStringExtra("byteIn");
                    String byteOut = intent.getStringExtra("byteOut");

                    if (status != null) {
                        currentStatus = status;
                        updateConnectionStatus(status);
                    }

                    if (duration != null && byteIn != null && byteOut != null) {
                        updateConnectionStats(duration, byteIn, byteOut);
                    }

                    Log.d(TAG, "VPN Status: " + status);
                } catch (Exception e) {
                    Log.e(TAG, "Error processing VPN status: " + e.getMessage());
                }
            }
        };

        IntentFilter filter = new IntentFilter(VPN_BROADCAST_ACTION);
        context.registerReceiver(vpnStatusReceiver, filter);
    }

    private void loadSavedServer() {
        selectedServer = ActiveServer.getSavedServer(context);
    }
    
    public boolean isVpnConnected() {
        return "CONNECTED".equals(currentStatus) || prefs.getBoolean(KEY_VPN_CONNECTED, false);
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public Countries getSelectedServer() {
        return selectedServer;
    }

    public void setSelectedServer(Countries server) {
        this.selectedServer = server;
        if (server != null) {
            ActiveServer.saveServer(server, context);
        }
    }
    
    public void connectVpn(Countries server) {
        try {
            if (listener != null) {
                listener.onConnecting();
            }

            // Validate server data
            if (server == null) {
                throw new IllegalArgumentException("Server cannot be null");
            }

            String country = server.getCountry();
            String ovpn = server.getOvpn();
            String username = server.getOvpnUserName();
            String password = server.getOvpnUserPassword();

            if (country == null || country.trim().isEmpty()) {
                throw new IllegalArgumentException("Country name cannot be null or empty");
            }

            if (ovpn == null || ovpn.trim().isEmpty()) {
                throw new IllegalArgumentException("OVPN configuration cannot be null or empty");
            }

            // Save selected server
            prefs.edit().putString(KEY_SELECTED_SERVER, country).apply();

            // Disconnect any existing connection first
            if (vpnThread != null) {
                try {
                    vpnThread.stop();
                } catch (Exception e) {
                    Log.w(TAG, "Error stopping existing VPN: " + e.getMessage());
                }
            }

            // Start VPN connection using OneConnect API
            // Provide default values for username/password if null
            String safeUsername = (username != null && !username.trim().isEmpty()) ? username : "";
            String safePassword = (password != null && !password.trim().isEmpty()) ? password : "";

            // Create new VPN thread and start connection
            vpnThread = new OpenVPNThread();
            OpenVpnApi.startVpn(context, ovpn, country, safeUsername, safePassword);

            // Update connection status
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, true).apply();

            if (listener != null) {
                listener.onConnected();
            }

            Log.d(TAG, "VPN connection started for: " + country);

        } catch (Exception e) {
            Log.e(TAG, "Error connecting VPN: " + e.getMessage());
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
            if (listener != null) {
                listener.onError(e.getMessage() != null ? e.getMessage() : "Unknown VPN error");
            }
        }
    }
    
    public void disconnectVpn() {
        try {
            // Method 1: Use the DisconnectVPNActivity approach
            try {
                Intent disconnectIntent = new Intent(context, Class.forName("top.oneconnectapi.app.DisconnectVPNActivity"));
                disconnectIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(disconnectIntent);
                Log.d(TAG, "DisconnectVPNActivity started");
            } catch (ClassNotFoundException e) {
                Log.w(TAG, "DisconnectVPNActivity class not found, trying alternative method: " + e.getMessage());

                // Method 2: Try to stop the VPN service directly
                try {
                    Intent serviceIntent = new Intent(context, Class.forName("top.oneconnectapi.app.core.OpenVPNService"));
                    context.stopService(serviceIntent);
                    Log.d(TAG, "OpenVPN service stop intent sent");
                } catch (ClassNotFoundException serviceException) {
                    Log.w(TAG, "OpenVPNService class not found: " + serviceException.getMessage());
                }
            }

            // Clear the thread reference without calling stop()
            if (vpnThread != null) {
                vpnThread = null;
                Log.d(TAG, "VPN thread reference cleared");
            }

            // Update connection status
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
            prefs.edit().remove(KEY_SELECTED_SERVER).apply();

            if (listener != null) {
                listener.onDisconnected();
            }

            Log.d(TAG, "VPN disconnected");

        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting VPN: " + e.getMessage());
            // Still update the status even if there was an error
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
            prefs.edit().remove(KEY_SELECTED_SERVER).apply();

            if (listener != null) {
                listener.onError(e.getMessage() != null ? e.getMessage() : "Unknown disconnection error");
            }
        }
    }
    
    public String getSelectedServerName() {
        return prefs.getString(KEY_SELECTED_SERVER, "Not Connected");
    }
    
    public List<Countries> getAvailableServers() {
        List<Countries> servers = new ArrayList<>();

        try {
            // Use OneConnect servers if available, otherwise use fallback
            String serverData = !Constants.FREE_SERVERS.isEmpty() ? Constants.FREE_SERVERS : Constants.FALLBACK_FREE_SERVERS;
            JSONArray jsonArray = new JSONArray(serverData);

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                servers.add(new Countries(
                    object.getString("serverName"),
                    object.getString("flag_url"),
                    object.getString("ovpnConfiguration"),
                    object.getString("vpnUserName"),
                    object.getString("vpnPassword")
                ));
            }

            Log.d(TAG, "Loaded " + servers.size() + " VPN servers");

        } catch (JSONException e) {
            Log.e(TAG, "Error parsing server data: " + e.getMessage());
            // Add fallback servers manually if JSON parsing fails
            servers.add(new Countries("United States", "🇺🇸",
                "client\ndev tun\nproto udp\nremote us-free.vpnbook.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nauth-user-pass\ncomp-lzo\nverb 3\nauth SHA1\ncipher BF-CBC",
                "vpnbook", "e8j64wq"));

            servers.add(new Countries("Germany", "🇩🇪",
                "client\ndev tun\nproto udp\nremote de-free.vpnbook.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nauth-user-pass\ncomp-lzo\nverb 3\nauth SHA1\ncipher BF-CBC",
                "vpnbook", "e8j64wq"));
        }

        return servers;
    }

    private void updateConnectionStatus(String status) {
        currentStatus = status;

        switch (status) {
            case "CONNECTED":
                prefs.edit().putBoolean(KEY_VPN_CONNECTED, true).apply();
                if (listener != null) {
                    listener.onConnected();
                    listener.onStatusChanged(status);
                }
                break;
            case "DISCONNECTED":
            case "USERPAUSE":
            case "NONETWORK":
                prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
                stopSpeedMonitoring();
                if (listener != null) {
                    listener.onDisconnected();
                    listener.onStatusChanged(status);
                }
                break;
            case "CONNECTING":
            case "WAIT":
            case "AUTH":
            case "GET_CONFIG":
            case "ASSIGN_IP":
            case "RECONNECTING":
                if (listener != null) {
                    listener.onConnecting();
                    listener.onStatusChanged(status);
                }
                break;
        }
    }

    private void updateConnectionStats(String duration, String byteIn, String byteOut) {
        if (listener != null) {
            listener.onConnectionStats(duration, byteIn, byteOut);
        }

        // Update speed calculation
        try {
            long currentTime = System.currentTimeMillis();
            long currentRxBytes = TrafficStats.getTotalRxBytes();
            long currentTxBytes = TrafficStats.getTotalTxBytes();

            if (mLastTime > 0) {
                long timeDiff = currentTime - mLastTime;
                long rxDiff = currentRxBytes - mLastRxBytes;
                long txDiff = currentTxBytes - mLastTxBytes;

                if (timeDiff > 0) {
                    speed.calcSpeed(timeDiff, rxDiff, txDiff);

                    if (listener != null) {
                        listener.onSpeedUpdate(
                            speed.down.speedValue + " " + speed.down.speedUnit,
                            speed.up.speedValue + " " + speed.up.speedUnit
                        );
                    }
                }
            }

            mLastTime = currentTime;
            mLastRxBytes = currentRxBytes;
            mLastTxBytes = currentTxBytes;

        } catch (Exception e) {
            Log.e(TAG, "Error updating speed stats: " + e.getMessage());
        }
    }

    private void startSpeedMonitoring() {
        if (speedHandler != null) {
            speedHandler.removeCallbacksAndMessages(null);
            speedHandler.postDelayed(speedUpdateRunnable, 1000);
        }
    }

    private void stopSpeedMonitoring() {
        if (speedHandler != null) {
            speedHandler.removeCallbacksAndMessages(null);
        }
    }

    private final Runnable speedUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            if ("CONNECTED".equals(currentStatus)) {
                updateConnectionStats("", "", "");
                speedHandler.postDelayed(this, 1000);
            }
        }
    };

    public void cleanup() {
        try {
            if (vpnStatusReceiver != null) {
                context.unregisterReceiver(vpnStatusReceiver);
            }
            stopSpeedMonitoring();
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup: " + e.getMessage());
        }
    }
}
