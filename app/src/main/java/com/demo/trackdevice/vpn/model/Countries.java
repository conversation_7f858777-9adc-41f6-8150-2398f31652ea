package com.demo.trackdevice.vpn.model;

import android.os.Parcel;
import android.os.Parcelable;

public class Countries implements Parcelable {
    private String country;
    private String flagUrl;
    private String ovpn;
    private String ovpnUserName;
    private String ovpnUserPassword;

    public Countries() {
    }

    public Countries(String country, String flagUrl, String ovpn) {
        this.country = country != null ? country : "";
        this.flagUrl = flagUrl != null ? flagUrl : "";
        this.ovpn = ovpn != null ? ovpn : "";
    }

    public Countries(String country, String flagUrl, String ovpn, String ovpnUserName, String ovpnUserPassword) {
        this.country = country != null ? country : "";
        this.flagUrl = flagUrl != null ? flagUrl : "";
        this.ovpn = ovpn != null ? ovpn : "";
        this.ovpnUserName = ovpnUserName != null ? ovpnUserName : "";
        this.ovpnUserPassword = ovpnUserPassword != null ? ovpnUserPassword : "";
    }

    public String getCountry() {
        return country != null ? country : "";
    }

    public void setCountry(String country) {
        this.country = country != null ? country : "";
    }

    public String getFlagUrl() {
        return flagUrl != null ? flagUrl : "";
    }

    public void setFlagUrl(String flagUrl) {
        this.flagUrl = flagUrl != null ? flagUrl : "";
    }

    public String getOvpn() {
        return ovpn != null ? ovpn : "";
    }

    public void setOvpn(String ovpn) {
        this.ovpn = ovpn != null ? ovpn : "";
    }

    public String getOvpnUserName() {
        return ovpnUserName != null ? ovpnUserName : "";
    }

    public void setOvpnUserName(String ovpnUserName) {
        this.ovpnUserName = ovpnUserName != null ? ovpnUserName : "";
    }

    public String getOvpnUserPassword() {
        return ovpnUserPassword != null ? ovpnUserPassword : "";
    }

    public void setOvpnUserPassword(String ovpnUserPassword) {
        this.ovpnUserPassword = ovpnUserPassword != null ? ovpnUserPassword : "";
    }

    public static final Creator<Countries> CREATOR = new Creator<Countries>() {
        public Countries createFromParcel(Parcel in) {
            return new Countries(in);
        }

        public Countries[] newArray(int size) {
            return new Countries[size];
        }
    };

    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(country);
        dest.writeString(flagUrl);
        dest.writeString(ovpn);
        dest.writeString(ovpnUserName);
        dest.writeString(ovpnUserPassword);
    }

    private Countries(Parcel in) {
        country = in.readString();
        flagUrl = in.readString();
        ovpn = in.readString();
        ovpnUserName = in.readString();
        ovpnUserPassword = in.readString();

        // Ensure no null values
        country = country != null ? country : "";
        flagUrl = flagUrl != null ? flagUrl : "";
        ovpn = ovpn != null ? ovpn : "";
        ovpnUserName = ovpnUserName != null ? ovpnUserName : "";
        ovpnUserPassword = ovpnUserPassword != null ? ovpnUserPassword : "";
    }
}
