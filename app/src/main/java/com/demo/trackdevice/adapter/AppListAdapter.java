package com.demo.trackdevice.adapter;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.R;

import java.io.File;
import java.text.DecimalFormat;
import java.util.List;

public class AppListAdapter extends ArrayAdapter<ApplicationInfo> {
    
    public final Context context;
    private final PackageManager packageManager;

    public AppListAdapter(Context context2, List<ApplicationInfo> list) {
        super(context2, 0, list);
        this.context = context2;
        this.packageManager = context2.getPackageManager();
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = LayoutInflater.from(getContext()).inflate(R.layout.list_item_app, viewGroup, false);
        }
        final ApplicationInfo applicationInfo = (ApplicationInfo) getItem(i);
        ImageView imageView = (ImageView) view.findViewById(R.id.app_icon);
        TextView textView = (TextView) view.findViewById(R.id.app_name);
        TextView textView2 = (TextView) view.findViewById(R.id.app_permissions);
        TextView textView3 = (TextView) view.findViewById(R.id.app_size);
        ImageView imageView2 = (ImageView) view.findViewById(R.id.delete_icon);
        if (applicationInfo != null) {
            imageView.setImageDrawable(applicationInfo.loadIcon(this.packageManager));
            textView.setText(applicationInfo.loadLabel(this.packageManager));
            String[] strArr = new String[0];
            try {
                String[] strArr2 = this.packageManager.getPackageInfo(applicationInfo.packageName, 4096).requestedPermissions;
                if (strArr2 != null) {
                    textView2.setText("Permissions: " + strArr2.length);
                } else {
                    textView2.setText("Permissions: 0");
                }
                textView3.setText(" " + formatFileSize(calculateAppSize(applicationInfo)));
                imageView2.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        AppListAdapter.this.context.startActivity(new Intent("android.intent.action.UNINSTALL_PACKAGE", Uri.parse("package:" + applicationInfo.packageName)));
                        AppPreferences.uninstallCall = true;
                    }
                });
            } catch (PackageManager.NameNotFoundException e) {
                throw new RuntimeException(e);
            }
        }
        return view;
    }

    private long calculateAppSize(ApplicationInfo applicationInfo) {
        return new File(applicationInfo.sourceDir).length() + getDirectorySize(new File(applicationInfo.dataDir));
    }

    private long getDirectorySize(File file) {
        File[] listFiles;
        long directorySize = 0;
        long j = 0;
        if (file.isDirectory() && (listFiles = file.listFiles()) != null) {
            for (File file2 : listFiles) {
                if (file2.isFile()) {
                    directorySize = file2.length();
                } else if (file2.isDirectory()) {
                    directorySize = getDirectorySize(file2);
                }
                j += directorySize;
            }
        }
        return j;
    }

    private String formatFileSize(long j) {
        if (j <= 0) {
            return "0 B";
        }
        String[] strArr = {"B", "KB", "MB", "GB", "TB"};
        double d = (double) j;
        int log10 = (int) (Math.log10(d) / Math.log10(1024.0d));
        return new DecimalFormat("#,##0.#").format(d / Math.pow(1024.0d, (double) log10)) + " " + strArr[log10];
    }
}
