package com.demo.trackdevice.activity;

import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.utils.NetworkSecurityChecker;
import com.demo.trackdevice.R;
import com.demo.trackdevice.ads.AdsCommon;

public class Network_Security_Activity extends AppCompatActivity {
    AppPreferences appPreferences;
    
    public TextView dnsStatusTextView;
    ImageView iv_dns;
    ImageView iv_network;
    ImageView iv_ssl;
    
    public TextView networkStatusTextView;
    ProgressBar pb_dns;
    ProgressBar pb_network;
    ProgressBar pb_ssl;
    
    public TextView sslStatusTextView;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().setStatusBarColor(-1);
        getWindow().getDecorView().setSystemUiVisibility(8192);
        setContentView(R.layout.activity_network_security);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }


        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        this.appPreferences = new AppPreferences(getApplicationContext());
        ((ImageView) findViewById(R.id.iv_back)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Network_Security_Activity.this.finish();
            }
        });
        this.networkStatusTextView = (TextView) findViewById(R.id.network_status_text_view);
        this.sslStatusTextView = (TextView) findViewById(R.id.ssl_status_text_view);
        this.dnsStatusTextView = (TextView) findViewById(R.id.dns_status_text_view);
        ((RelativeLayout) findViewById(R.id.RL_TrustEveryone)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Network_Security_Activity.this.appPreferences.setNetworkSecurityTrusted(true);
                Network_Security_Activity.this.finish();
            }
        });
        this.iv_network = (ImageView) findViewById(R.id.iv_network);
        this.iv_ssl = (ImageView) findViewById(R.id.iv_ssl);
        this.iv_dns = (ImageView) findViewById(R.id.iv_dns);
        this.pb_network = (ProgressBar) findViewById(R.id.pb_network);
        this.pb_ssl = (ProgressBar) findViewById(R.id.pb_ssl);
        this.pb_dns = (ProgressBar) findViewById(R.id.pb_dns);
        new NetworkSecurityTask().execute(new Void[0]);
    }

    private class NetworkSecurityTask extends AsyncTask<Void, Void, String[]> {
        private NetworkSecurityTask() {
        }

        @Override
        public String[] doInBackground(Void... voidArr) {
            NetworkSecurityChecker networkSecurityChecker = new NetworkSecurityChecker(Network_Security_Activity.this.getApplicationContext());
            return new String[]{networkSecurityChecker.checkNetworkSecurity(), networkSecurityChecker.checkSSLStatus(), networkSecurityChecker.checkDNSStatus()};
        }

        @Override
        public void onPostExecute(final String[] strArr) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (strArr[0].equalsIgnoreCase("WIFI")) {
                        Network_Security_Activity.this.networkStatusTextView.setText("You are connected to a stable WiFi network.");
                        Network_Security_Activity.this.iv_network.setVisibility(0);
                        Network_Security_Activity.this.pb_network.setVisibility(8);
                        Network_Security_Activity.this.iv_network.setImageResource(R.drawable.right_icon);
                    } else if (strArr[0].equalsIgnoreCase("MOBILE")) {
                        Network_Security_Activity.this.networkStatusTextView.setText("You are connected to a stable cellular network.");
                        Network_Security_Activity.this.iv_network.setVisibility(0);
                        Network_Security_Activity.this.pb_network.setVisibility(8);
                        Network_Security_Activity.this.iv_network.setImageResource(R.drawable.right_icon);
                    } else if (strArr[0].equalsIgnoreCase("OFF")) {
                        Network_Security_Activity.this.networkStatusTextView.setText("There is no active network connection.");
                        Network_Security_Activity.this.iv_network.setVisibility(0);
                        Network_Security_Activity.this.pb_network.setVisibility(8);
                        Network_Security_Activity.this.iv_network.setImageResource(R.drawable.wrong_icon);
                    }
                }
            }, 1000);
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (strArr[1].equalsIgnoreCase("Valid")) {
                        Network_Security_Activity.this.sslStatusTextView.setText("Nothing can break the SSL connection, therefore your data is safe.");
                        Network_Security_Activity.this.iv_ssl.setVisibility(0);
                        Network_Security_Activity.this.pb_ssl.setVisibility(8);
                        Network_Security_Activity.this.iv_ssl.setImageResource(R.drawable.right_icon);
                    } else if (strArr[1].equalsIgnoreCase("Invalid")) {
                        Network_Security_Activity.this.sslStatusTextView.setText("A few SSL security issues. Do you think this WiFi network is trustworthy?");
                        Network_Security_Activity.this.iv_ssl.setVisibility(0);
                        Network_Security_Activity.this.pb_ssl.setVisibility(8);
                        Network_Security_Activity.this.iv_ssl.setImageResource(R.drawable.wrong_icon);
                    }
                }
            }, 3000);
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (strArr[2].equalsIgnoreCase("Valid")) {
                        Network_Security_Activity.this.dnsStatusTextView.setText("The DNS provider you are using is secure.");
                        Network_Security_Activity.this.iv_dns.setVisibility(0);
                        Network_Security_Activity.this.pb_dns.setVisibility(8);
                        Network_Security_Activity.this.iv_dns.setImageResource(R.drawable.right_icon);
                    } else if (strArr[2].equalsIgnoreCase("Invalid")) {
                        Network_Security_Activity.this.dnsStatusTextView.setText("DNS security is experiencing some issues. How confident are you in this WiFi network?");
                        Network_Security_Activity.this.iv_dns.setVisibility(0);
                        Network_Security_Activity.this.pb_dns.setVisibility(8);
                        Network_Security_Activity.this.iv_dns.setImageResource(R.drawable.wrong_icon);
                    }
                }
            }, 5000);
        }
    }
}
