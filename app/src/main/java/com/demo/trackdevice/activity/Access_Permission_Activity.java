package com.demo.trackdevice.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.appcompat.app.AppCompatActivity;

import com.demo.trackdevice.R;
import com.demo.trackdevice.ads.AdsCommon;

public class Access_Permission_Activity extends AppCompatActivity {
    
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().setStatusBarColor(-1);
        getWindow().getDecorView().setSystemUiVisibility(8192);
        setContentView(R.layout.activity_access_permission);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }


        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        ((ImageView) findViewById(R.id.iv_back)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Access_Permission_Activity.this.finish();
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Camara_permission)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Access_Permission_Activity.this, Camera_Permission_Activity.class);
                AdsCommon.InterstitialAd(Access_Permission_Activity.this, intent);
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Location_permission)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Access_Permission_Activity.this, Location_Permission_Activity.class);
                AdsCommon.InterstitialAd(Access_Permission_Activity.this, intent);
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Storage_permission)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Access_Permission_Activity.this, Storage_Permission_Activity.class);
                AdsCommon.InterstitialAd(Access_Permission_Activity.this, intent);
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Contact_permission)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Access_Permission_Activity.this, Contact_Permission_Activity.class);
                AdsCommon.InterstitialAd(Access_Permission_Activity.this, intent);
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Mic_permission)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Access_Permission_Activity.this, Mic_Permission_Activity.class);
                AdsCommon.InterstitialAd(Access_Permission_Activity.this, intent);
            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }

}
