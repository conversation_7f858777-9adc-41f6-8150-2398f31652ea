package com.demo.trackdevice.activity;

import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StatFs;
import android.provider.Settings;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.R;
import com.demo.trackdevice.ads.AdsCommon;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class Device_Info_Activity extends AppCompatActivity {
    private static final long BYTES_PER_GB = **********;
    AppPreferences appPreferences;

    private static double bytesToGB(long j) {
        return ((double) j) / 1.073741824E9d;
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().getDecorView().setSystemUiVisibility(1280);
        setWindowFlag(this, 67108864, false);
        getWindow().setStatusBarColor(0);
        setContentView(R.layout.activity_device_info);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }


        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        ((ImageView) findViewById(R.id.iv_back)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Device_Info_Activity.this.finish();
            }
        });
        ((TextView) findViewById(R.id.tvDeviceModel)).setText(Build.BRAND + " " + Build.MODEL);
        ((TextView) findViewById(R.id.tvVersionNo)).setText("Android " + Build.VERSION.RELEASE);
        int[] screenResolution = getScreenResolution(this);
        ((TextView) findViewById(R.id.tvScreen)).setText(screenResolution[0] + " x " + screenResolution[1]);
        TextView textView = (TextView) findViewById(R.id.tv_bluetooth);
        TextView textView2 = (TextView) findViewById(R.id.tv_Location);
        TextView textView3 = (TextView) findViewById(R.id.tv_Aeroplane);
        TextView textView4 = (TextView) findViewById(R.id.tv_brightness);
        if (isBluetoothEnabled()) {
            textView.setText("On");
        } else {
            textView.setText("Off");
        }
        if (isGPSEnabled(this)) {
            textView2.setText("On");
        } else {
            textView2.setText("Off");
        }
        if (isAirplaneModeOn(this)) {
            textView3.setText("On");
        } else {
            textView3.setText("Off");
        }
        textView4.setText(getScreenBrightness(this) + "%");
        int batteryPercentage = getBatteryPercentage(this);
        ((ProgressBar) findViewById(R.id.pb_Charging)).setProgress(batteryPercentage);
        ((TextView) findViewById(R.id.tv_charging)).setText(batteryPercentage + "%");
        ((TextView) findViewById(R.id.tv_wifi)).setText(getWifiSignalStrengthPercentage(this) + "/100");
        ((ProgressBar) findViewById(R.id.pb_WiFi)).setProgress(getWifiSignalStrengthPercentage(this));
        getStorageInfo();
        ((TextView) findViewById(R.id.tvtotalapps)).setText(getTotalInstalledApps(this) + " Apps");
        ((TextView) findViewById(R.id.tv_totalsize)).setText(String.format("%.2f GB", new Object[]{Double.valueOf(getTotalAppSizeGB(this))}));
        this.appPreferences = new AppPreferences(getApplicationContext());
        ((RelativeLayout) findViewById(R.id.RL_TrustEveryone)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Device_Info_Activity.this.appPreferences.setDeviceInfoTrusted(true);
                Device_Info_Activity.this.finish();
            }
        });
    }

    public static void setWindowFlag(Activity activity, int i, boolean z) {
        Window window = activity.getWindow();
        WindowManager.LayoutParams attributes = window.getAttributes();
        if (z) {
            attributes.flags = i | attributes.flags;
        } else {
            attributes.flags = (~i) & attributes.flags;
        }
        window.setAttributes(attributes);
    }

    private static int[] getScreenResolution(Context context) {
        Display defaultDisplay = ((WindowManager) context.getSystemService("window")).getDefaultDisplay();
        DisplayMetrics displayMetrics = new DisplayMetrics();
        defaultDisplay.getMetrics(displayMetrics);
        return new int[]{displayMetrics.widthPixels, displayMetrics.heightPixels};
    }

    private static boolean isBluetoothEnabled() {
        BluetoothAdapter defaultAdapter = BluetoothAdapter.getDefaultAdapter();
        if (defaultAdapter == null) {
            return false;
        }
        return defaultAdapter.isEnabled();
    }

    private static boolean isGPSEnabled(Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService("location");
        if (locationManager != null) {
            return locationManager.isProviderEnabled("gps");
        }
        return false;
    }

    private static boolean isAirplaneModeOn(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), "airplane_mode_on", 0) == 1;
    }

    public static int getScreenBrightness(Context context) {
        try {
            if (Settings.System.getInt(context.getContentResolver(), "screen_brightness_mode") == 1) {
                return -1;
            }
            return (Settings.System.getInt(context.getContentResolver(), "screen_brightness") * 100) / 255;
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static int getBatteryPercentage(Context context) {
        Intent registerReceiver = context.registerReceiver((BroadcastReceiver) null, new IntentFilter("android.intent.action.BATTERY_CHANGED"));
        if (registerReceiver != null) {
            int intExtra = registerReceiver.getIntExtra("level", -1);
            int intExtra2 = registerReceiver.getIntExtra("scale", -1);
            if (!(intExtra == -1 || intExtra2 == -1)) {
                return (int) ((((float) intExtra) / ((float) intExtra2)) * 100.0f);
            }
        }
        return -1;
    }

    public static int getWifiSignalStrengthPercentage(Context context) {
        WifiInfo connectionInfo;
        WifiManager wifiManager = (WifiManager) context.getSystemService("wifi");
        if (wifiManager == null || (connectionInfo = wifiManager.getConnectionInfo()) == null) {
            return -1;
        }
        return Math.max(0, Math.min(((connectionInfo.getRssi() + 100) * 100) / 50, 100));
    }

    public static double getTotalStorageGB() {
        StatFs statFs = new StatFs(Environment.getExternalStorageDirectory().getAbsolutePath());
        return bytesToGB(statFs.getBlockCountLong() * statFs.getBlockSizeLong());
    }

    public static double getUsedStorageGB() {
        StatFs statFs = new StatFs(Environment.getExternalStorageDirectory().getAbsolutePath());
        return bytesToGB((statFs.getBlockCountLong() - statFs.getAvailableBlocksLong()) * statFs.getBlockSizeLong());
    }

    private void getStorageInfo() {
        StatFs statFs = new StatFs(Environment.getExternalStorageDirectory().getPath());
        long blockCountLong = statFs.getBlockCountLong() * statFs.getBlockSizeLong();
        float f = ((float) blockCountLong) / 1.07374182E9f;
        float availableBlocksLong = ((float) (blockCountLong - (statFs.getAvailableBlocksLong() * statFs.getBlockSizeLong()))) / 1.07374182E9f;
        updateStorageInfo(f, availableBlocksLong, (availableBlocksLong / f) * 100.0f);
    }

    private void updateStorageInfo(float f, float f2, float f3) {
        ((TextView) findViewById(R.id.tv_storage)).setText(String.format("%.2f", new Object[]{Float.valueOf(f2)}) + "/" + String.format("%.2f GB", new Object[]{Float.valueOf(f)}));
        ((ProgressBar) findViewById(R.id.pb_storage)).setProgress((int) f3);
    }

    public int getTotalInstalledApps(Context context) {
        return context.getPackageManager().getInstalledApplications(128).size();
    }

    public static double getTotalAppSizeGB(Context context) {
        PackageManager packageManager = context.getPackageManager();
        long j = 0;
        for (ApplicationInfo applicationInfo : packageManager.getInstalledApplications(128)) {
            try {
                j += new File(packageManager.getPackageInfo(applicationInfo.packageName, 0).applicationInfo.publicSourceDir).length();
            } catch (PackageManager.NameNotFoundException e) {
                e.printStackTrace();
            }
        }
        return bytesToGB(j);
    }

    private List<ApplicationInfo> getNonSystemApps() {
        List<ApplicationInfo> installedApplications = getPackageManager().getInstalledApplications(0);
        ArrayList arrayList = new ArrayList();
        for (ApplicationInfo add : installedApplications) {
            arrayList.add(add);
        }
        return arrayList;
    }
}
