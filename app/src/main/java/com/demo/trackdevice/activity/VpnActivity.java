package com.demo.trackdevice.activity;

import android.content.Intent;
import android.net.VpnService;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.demo.trackdevice.R;
import com.demo.trackdevice.ads.AdsCommon;
import com.demo.trackdevice.vpn.VpnManager;
import com.demo.trackdevice.vpn.adapter.ServerAdapter;
import com.demo.trackdevice.vpn.model.Countries;

import java.util.List;

public class VpnActivity extends AppCompatActivity implements VpnManager.VpnConnectionListener {
    
    private static final int VPN_REQUEST_CODE = 100;
    
    private Button btnConnect;
    private TextView tvConnectionStatus;
    private TextView tvSelectedServer;
    private RecyclerView recyclerViewServers;
    private ServerAdapter serverAdapter;
    
    private VpnManager vpnManager;
    private Countries selectedServer;
    private List<Countries> serverList;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_vpn);
        
        initViews();
        setupVpnManager();
        setupServerList();
        updateUI();
        
        // Show banner ad - will be implemented later
        // AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);
    }
    
    private void initViews() {
        btnConnect = findViewById(R.id.btn_connect);
        tvConnectionStatus = findViewById(R.id.tv_connection_status);
        tvSelectedServer = findViewById(R.id.tv_selected_server);
        recyclerViewServers = findViewById(R.id.recycler_servers);
        
        btnConnect.setOnClickListener(v -> toggleVpnConnection());
    }
    
    private void setupVpnManager() {
        vpnManager = new VpnManager(this);
        vpnManager.setConnectionListener(this);
    }
    
    private void setupServerList() {
        serverList = vpnManager.getAvailableServers();
        selectedServer = serverList.get(0); // Default to first server
        
        serverAdapter = new ServerAdapter(serverList, server -> {
            selectedServer = server;
            tvSelectedServer.setText("Selected: " + server.getCountry());
            updateUI();
        });
        
        recyclerViewServers.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewServers.setAdapter(serverAdapter);
    }
    
    private void toggleVpnConnection() {
        if (vpnManager.isVpnConnected()) {
            vpnManager.disconnectVpn();
        } else {
            // Request VPN permission
            Intent intent = VpnService.prepare(this);
            if (intent != null) {
                startActivityForResult(intent, VPN_REQUEST_CODE);
            } else {
                connectToVpn();
            }
        }
    }
    
    private void connectToVpn() {
        if (selectedServer != null) {
            vpnManager.connectVpn(selectedServer);
        } else {
            Toast.makeText(this, "Please select a server", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void updateUI() {
        if (vpnManager.isVpnConnected()) {
            btnConnect.setText("Disconnect");
            btnConnect.setBackgroundColor(getResources().getColor(android.R.color.holo_red_light));
            tvConnectionStatus.setText("Connected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            tvSelectedServer.setText("Connected to: " + vpnManager.getSelectedServer());
        } else {
            btnConnect.setText("Connect");
            btnConnect.setBackgroundColor(getResources().getColor(android.R.color.holo_green_light));
            tvConnectionStatus.setText("Disconnected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            tvSelectedServer.setText("Selected: " + (selectedServer != null ? selectedServer.getCountry() : "None"));
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == VPN_REQUEST_CODE && resultCode == RESULT_OK) {
            connectToVpn();
        } else {
            Toast.makeText(this, "VPN permission denied", Toast.LENGTH_SHORT).show();
        }
    }
    
    // VpnManager.VpnConnectionListener implementation
    @Override
    public void onConnected() {
        runOnUiThread(() -> {
            updateUI();
            Toast.makeText(this, "VPN Connected", Toast.LENGTH_SHORT).show();
        });
    }
    
    @Override
    public void onDisconnected() {
        runOnUiThread(() -> {
            updateUI();
            Toast.makeText(this, "VPN Disconnected", Toast.LENGTH_SHORT).show();
        });
    }
    
    @Override
    public void onConnecting() {
        runOnUiThread(() -> {
            tvConnectionStatus.setText("Connecting...");
            btnConnect.setEnabled(false);
        });
    }
    
    @Override
    public void onError(String error) {
        runOnUiThread(() -> {
            updateUI();
            btnConnect.setEnabled(true);
            Toast.makeText(this, "VPN Error: " + error, Toast.LENGTH_LONG).show();
        });
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        updateUI();
    }
}
