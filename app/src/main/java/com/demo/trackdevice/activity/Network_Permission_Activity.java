package com.demo.trackdevice.activity;

import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

import com.demo.trackdevice.adapter.AppListAdapter;
import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.utils.ProcessDoneDialog;
import com.demo.trackdevice.R;
import com.demo.trackdevice.ads.AdsCommon;

import java.util.ArrayList;
import java.util.List;

public class Network_Permission_Activity extends AppCompatActivity {
    AppListAdapter adapter;
    ListView appListView;
    AppPreferences appPreferences;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().setStatusBarColor(-1);
        getWindow().getDecorView().setSystemUiVisibility(8192);
        setContentView(R.layout.activity_camera_permission);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }


        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        ImageView imageView = (ImageView) findViewById(R.id.iv_back);
        TextView textView = (TextView) findViewById(R.id.tv_Title);
        TextView textView2 = (TextView) findViewById(R.id.tv_TopText);
        final TextView textView3 = (TextView) findViewById(R.id.tv_trusted);
        final ImageView imageView2 = (ImageView) findViewById(R.id.iv_trusted);
        AppPreferences appPreferences2 = new AppPreferences(getApplicationContext());
        this.appPreferences = appPreferences2;
        if (appPreferences2.getNetworkPermissionTrusted()) {
            textView3.setText(R.string.trusted);
            imageView2.setVisibility(0);
        } else {
            textView3.setText(R.string.trust_everyone);
            imageView2.setVisibility(8);
        }
        ((RelativeLayout) findViewById(R.id.RL_TrustEveryone)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ProcessDoneDialog.show(Network_Permission_Activity.this, "All apps are marked as trusted.");
                Network_Permission_Activity.this.appPreferences.setNetworkPermissionTrusted(true);
                textView3.setText(R.string.trusted);
                imageView2.setVisibility(0);
            }
        });
        imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Network_Permission_Activity.this.finish();
            }
        });
        textView.setText("Network Access");
        textView2.setText("Check network access apps to ensure your privacy and security. View and configure which apps have permission to access your device’s network.");
        AdapterCall();
    }

    
    public void AdapterCall() {
        this.appListView = (ListView) findViewById(R.id.app_list_view);
        List<ApplicationInfo> nonSystemApps = getNonSystemApps();
        final ArrayList arrayList = new ArrayList();
        for (ApplicationInfo next : nonSystemApps) {
            if (checkSensitivePermission(next.packageName)) {
                arrayList.add(next);
            }
        }
        AppListAdapter appListAdapter = new AppListAdapter(this, arrayList);
        this.adapter = appListAdapter;
        this.appListView.setAdapter(appListAdapter);
        this.appListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
                Intent intent = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS");
                intent.addFlags(268435456);
                intent.setData(Uri.fromParts("package", ((ApplicationInfo) arrayList.get(i)).packageName, (String) null));
                Network_Permission_Activity.this.startActivity(intent);
            }
        });
    }

    private List<ApplicationInfo> getInstalledApps() {
        PackageManager packageManager = getPackageManager();
        new Intent("android.intent.action.MAIN", (Uri) null).addCategory("android.intent.category.LAUNCHER");
        return packageManager.getInstalledApplications(0);
    }

    private List<ApplicationInfo> getNonSystemApps() {
        List<ApplicationInfo> installedApplications = getPackageManager().getInstalledApplications(0);
        ArrayList arrayList = new ArrayList();
        for (ApplicationInfo next : installedApplications) {
            if ((next.flags & 1) == 0) {
                arrayList.add(next);
            }
        }
        return arrayList;
    }

    private boolean checkSensitivePermission(String str) {
        try {
            String[] strArr = getPackageManager().getPackageInfo(str, 4096).requestedPermissions;
            if (strArr != null) {
                for (String equals : strArr) {
                    if (equals.equals("android.permission.INTERNET")) {
                        return true;
                    }
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return false;
    }

    
    @Override
    public void onResume() {
        super.onResume();
        if (AppPreferences.uninstallCall) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Network_Permission_Activity.this.AdapterCall();
                }
            }, 1000);
            AppPreferences.uninstallCall = false;
        }
    }
}
