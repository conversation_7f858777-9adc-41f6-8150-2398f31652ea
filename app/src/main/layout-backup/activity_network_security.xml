<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_above="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/RL_TrustEveryone"
            android:background="#ffffff"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:padding="10dp">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:src="@drawable/back_arrow" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:text="Network"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="20dp"
                        android:src="@drawable/network_security_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="Check your network security"
                        android:textColor="#000000"
                        android:textSize="20sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="25dp"
                        android:background="#d9d9d9" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="15dp">

                        <TextView
                            android:id="@+id/network_status_text_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="10dp"
                            android:layout_toStartOf="@+id/ll_network"
                            android:text="Network Status"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <LinearLayout
                            android:id="@+id/ll_network"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/iv_network"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:src="@drawable/delete_icon"
                                android:visibility="gone" />

                            <ProgressBar
                                android:id="@+id/pb_network"
                                android:layout_width="25dp"
                                android:layout_height="25dp" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="15dp">

                        <TextView
                            android:id="@+id/ssl_status_text_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="10dp"
                            android:layout_toStartOf="@+id/ll_ssl"
                            android:text="SSL Security"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <LinearLayout
                            android:id="@+id/ll_ssl"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/iv_ssl"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:src="@drawable/delete_icon"
                                android:visibility="gone" />

                            <ProgressBar
                                android:id="@+id/pb_ssl"
                                android:layout_width="25dp"
                                android:layout_height="25dp" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="15dp">

                        <TextView
                            android:id="@+id/dns_status_text_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="10dp"
                            android:layout_toStartOf="@+id/ll_dns"
                            android:text="DNS Security"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <LinearLayout
                            android:id="@+id/ll_dns"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/iv_dns"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:src="@drawable/delete_icon"
                                android:visibility="gone" />

                            <ProgressBar
                                android:id="@+id/pb_dns"
                                android:layout_width="25dp"
                                android:layout_height="25dp" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="15dp"
                        android:text="When using free Wi-Fi networks, use caution. Connect to Wi-Fi networks that you can trust exclusively to protect your personal information."
                        android:textColor="#959595"
                        android:textSize="15sp" />
                </LinearLayout>
            </ScrollView>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_above="@+id/RL_TrustEveryone"
            android:background="@drawable/top_shadow_gradient" />

        <RelativeLayout
            android:id="@+id/RL_TrustEveryone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/primarymain"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_trusted"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="13dp"
                    android:background="@color/primarymain"
                    android:gravity="center"
                    android:text="DONE"
                    android:textColor="#ffffff"
                    android:textSize="19sp" />
            </LinearLayout>
        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>
