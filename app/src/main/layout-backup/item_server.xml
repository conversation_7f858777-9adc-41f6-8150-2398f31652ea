<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_country_flag"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="🇺🇸"
            android:textSize="24sp"
            android:gravity="center"
            android:background="@drawable/circle_background"
            android:layout_marginEnd="16dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_country_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="United States"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Free Server"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_forward"
            android:tint="@android:color/darker_gray" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
