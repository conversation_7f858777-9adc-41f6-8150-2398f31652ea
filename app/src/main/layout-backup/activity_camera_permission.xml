<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_above="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/main_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/RL_TrustEveryone"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:padding="10dp">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:src="@drawable/back_arrow" />

                <TextView
                    android:id="@+id/tv_Title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:text="App Access"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_TopText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="15dp"
                android:layout_marginBottom="15dp"
                android:text="We have detected applications that have access to the camera. Do you trust them? If not, you can delete them!"
                android:textColor="#9e9e9e"
                android:textSize="17sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#d9d9d9" />

            <ListView
                android:id="@+id/app_list_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:divider="@null" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_above="@+id/RL_TrustEveryone"
            android:background="@drawable/top_shadow_gradient" />

        <RelativeLayout
            android:id="@+id/RL_TrustEveryone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/primarymain"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_trusted"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="13dp"
                    android:background="@color/primarymain"
                    android:gravity="center"
                    android:text="TRUST EVERYONE"
                    android:textColor="#ffffff"
                    android:textSize="19sp" />

                <ImageView
                    android:id="@+id/iv_trusted"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="2dp"
                    android:src="@drawable/trust_right_icon"
                    android:visibility="visible" />
            </LinearLayout>
        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>
