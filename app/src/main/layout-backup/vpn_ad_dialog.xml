<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="20dp"
        android:background="@drawable/rounded_dialog_background"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Ad Title -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔒 Secure Connection Loading..."
            android:textAlignment="center"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="15dp" />

        <!-- Ad Content Area -->
        <FrameLayout
            android:id="@+id/ad_container"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:background="#f5f5f5"
            android:layout_marginBottom="15dp">

            <!-- Placeholder for actual ad -->
            <LinearLayout
                android:id="@+id/ad_placeholder"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:background="#e0e0e0">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/logo"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Loading Advertisement..."
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <ProgressBar
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    style="?android:attr/progressBarStyleSmall" />

            </LinearLayout>

        </FrameLayout>

        <!-- Timer and Skip Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Connecting to secure server..."
                android:textColor="#666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5s"
                android:textColor="#ff6b35"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/timer_background"
                android:padding="8dp"
                android:minWidth="40dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- Skip Button -->
        <Button
            android:id="@+id/btn_skip"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="Skip (5s)"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/skip_button_background"
            android:enabled="false"
            android:alpha="0.5" />

    </LinearLayout>

</RelativeLayout>
