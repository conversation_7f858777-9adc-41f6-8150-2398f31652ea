<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:layout_above="@+id/btm1"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:padding="10dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:src="@drawable/back_arrow" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="10dp"
                android:text="Network"
                android:textColor="#000000"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <RelativeLayout
                    android:id="@+id/RL_Network_permission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/network_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext"
                        android:layout_toEndOf="@+id/icImage"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Network Access"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Secure network access!"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Network_Security"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage5"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/network_security_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext5"
                        android:layout_toEndOf="@+id/icImage5"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Network Security"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="check network security"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext5"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />
            </LinearLayout>
        </ScrollView>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>
