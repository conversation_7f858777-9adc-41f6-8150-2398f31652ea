<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:padding="3dp"
    android:layout_width="match_parent"
    android:background="@color/colorlightdark"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/native_ad_call_to_action"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/content"
                android:layout_alignParentRight="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="3dp"
                android:layout_marginTop="3dp"
                android:layout_marginEnd="3dp"
                android:layout_marginBottom="3dp"
                android:background="@drawable/instgradient"
                android:gravity="center"
                android:paddingLeft="3dp"
                android:paddingRight="3dp"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:visibility="visible" />

            <LinearLayout
                android:gravity="center"
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="2dp"
                android:layout_toRightOf="@+id/native_icon_view">

                <RelativeLayout
                    android:id="@+id/ad_choices_container"
                    android:padding="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:textColor="@color/adstext"
                    android:ellipsize="end"
                    android:id="@+id/native_ad_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />
            </LinearLayout>

            <TextView
                android:textSize="12sp"
                android:textColor="@color/adstext"
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:id="@+id/native_ad_sponsored_label"
                android:padding="2dp"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:lines="1"
                android:layout_toRightOf="@+id/native_icon_view"
                android:layout_below="@+id/content" />

            <TextView
                android:textSize="11sp"
                android:textColor="@color/adstext"
                android:ellipsize="end"
                android:gravity="left|center_vertical|center_horizontal|center"
                android:id="@+id/native_ad_social_context"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:layout_marginRight="5dp"
                android:layout_toLeftOf="@+id/native_ad_call_to_action"
                android:layout_below="@+id/native_icon_view"
                android:layout_centerInParent="true" />

            <com.facebook.ads.MediaView
                android:gravity="center"
                android:id="@+id/native_icon_view"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_alignParentLeft="true" />
        </RelativeLayout>
    </RelativeLayout>
</LinearLayout>
