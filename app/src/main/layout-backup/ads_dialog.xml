<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="#00000000">

    <LinearLayout
        android:id="@+id/nointernet"
        android:layout_width="280dp"
        android:layout_height="100dp"
        android:layout_margin="10dp"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:fontFamily="@font/regular"
            android:text="PLEASE WAIT...!!"
            android:textColor="@color/black"
            android:textSize="20dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:fontFamily="@font/regular"
            android:text="ADS Is Loading......"
            android:textColor="@color/black"
            android:textSize="15dp" />

    </LinearLayout>

</FrameLayout>
