<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:orientation="horizontal"
        android:paddingStart="10dp"
        android:paddingTop="15dp"
        android:paddingEnd="10dp"
        android:paddingBottom="15dp">

        <ImageView
            android:id="@+id/app_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/logo" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/app_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:singleLine="true"
                    android:textColor="@android:color/black"
                    android:textSize="17sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/app_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:singleLine="true"
                    android:textColor="#9d9d9d"
                    android:textSize="15sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/app_permissions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="#9d9d9d"
                android:textSize="15sp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/delete_icon"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_gravity="center_vertical"
            android:padding="5dp"
            android:src="@drawable/delete_icon" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#d9d9d9" />

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="#f3f3f3" />
</LinearLayout>
