<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:layout_above="@+id/btm1"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:padding="10dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:src="@drawable/back_arrow" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="10dp"
                android:text="App Access"
                android:textColor="#000000"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <RelativeLayout
                    android:id="@+id/RL_Camara_permission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/access_camera_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext"
                        android:layout_toEndOf="@+id/icImage"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Who is using your camera?"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Protect your camera&apos;s privacy"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Location_permission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage2"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/access_location_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext2"
                        android:layout_toEndOf="@+id/icImage2"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Who is using your location?"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Protect your location data"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext2"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Storage_permission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage3"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/access_storage_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext3"
                        android:layout_toEndOf="@+id/icImage3"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Who is using your storage?"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Protect your storage data"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext3"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Contact_permission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage4"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/access_contact_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext4"
                        android:layout_toEndOf="@+id/icImage4"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Who knows your contacts?"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Protect your contacts"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext4"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Mic_permission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage5"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/access_mic_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext5"
                        android:layout_toEndOf="@+id/icImage5"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Who is using your mic?"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Protect your microphone access"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext5"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />
            </LinearLayout>
        </ScrollView>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>
