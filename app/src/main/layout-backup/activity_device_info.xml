<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff">

    <RelativeLayout
        android:layout_above="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/RL_TrustEveryone"
            android:background="#ffffff"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/primarymain"
                android:paddingLeft="10dp"
                android:paddingTop="40dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:src="@drawable/back_arrow_white" />

                <TextView
                    android:id="@+id/tv_Title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:text="Device Information"
                    android:textColor="#ffffff"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="300dp"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="150dp"
                            android:background="@color/primarymain">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginStart="25dp"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvDeviceModel"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="OnePlus Nord 3"
                                    android:textColor="#ffffff"
                                    android:textSize="25sp" />

                                <TextView
                                    android:id="@+id/tvVersionNo"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="6dp"
                                    android:text="Android 11"
                                    android:textColor="#ffffff"
                                    android:textSize="17sp" />

                                <TextView
                                    android:id="@+id/tvScreen"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="6dp"
                                    android:text="Android 11"
                                    android:textColor="#ffffff"
                                    android:textSize="15sp" />
                            </LinearLayout>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="150dp"
                                android:orientation="vertical"
                                android:weightSum="4">

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="0dp"
                                    android:layout_weight="2"
                                    android:background="@color/primarymain" />

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="0dp"
                                    android:layout_weight="2" />
                            </LinearLayout>

                            <androidx.cardview.widget.CardView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="25dp"
                                android:layout_marginEnd="25dp"
                                app:cardBackgroundColor="#ffffff"
                                app:cardCornerRadius="15dp"
                                app:cardElevation="5dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingTop="15dp"
                                    android:paddingBottom="15dp">

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="60dp"
                                            android:layout_height="60dp"
                                            android:layout_gravity="center_horizontal"
                                            android:src="@drawable/bluetooth_icon" />

                                        <TextView
                                            android:id="@+id/tv_bluetooth"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center_horizontal"
                                            android:layout_marginTop="6dp"
                                            android:text="Off"
                                            android:textColor="#888585"
                                            android:textSize="15sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="60dp"
                                            android:layout_height="60dp"
                                            android:layout_gravity="center_horizontal"
                                            android:src="@drawable/location_info" />

                                        <TextView
                                            android:id="@+id/tv_Location"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center_horizontal"
                                            android:layout_marginTop="6dp"
                                            android:text="Off"
                                            android:textColor="#888585"
                                            android:textSize="15sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="60dp"
                                            android:layout_height="60dp"
                                            android:layout_gravity="center_horizontal"
                                            android:src="@drawable/airplane_icon" />

                                        <TextView
                                            android:id="@+id/tv_Aeroplane"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center_horizontal"
                                            android:layout_marginTop="6dp"
                                            android:text="Off"
                                            android:textColor="#888585"
                                            android:textSize="15sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="60dp"
                                            android:layout_height="60dp"
                                            android:layout_gravity="center_horizontal"
                                            android:src="@drawable/brightness_icon" />

                                        <TextView
                                            android:id="@+id/tv_brightness"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center_horizontal"
                                            android:layout_marginTop="6dp"
                                            android:text="Off"
                                            android:textColor="#888585"
                                            android:textSize="15sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>
                                </LinearLayout>
                            </androidx.cardview.widget.CardView>
                        </RelativeLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="6dp"
                        android:background="#d9d9d9" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="10dp"
                        android:padding="10dp">

                        <ImageView
                            android:id="@+id/icCharging"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/charging" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="20dp"
                            android:layout_toEndOf="@+id/icCharging"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Battery"
                                    android:textColor="#000000" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="5dp"
                                        android:text="Charged"
                                        android:textColor="#9e9e9e" />

                                    <TextView
                                        android:id="@+id/tv_charging"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="15%"
                                        android:textColor="#000000" />
                                </LinearLayout>
                            </RelativeLayout>

                            <ProgressBar
                                android:id="@+id/pb_Charging"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:progress="10"
                                android:progressBackgroundTint="#d9d9d9"
                                android:progressTint="@color/primarymain" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:background="#f3f3f3" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="10dp"
                        android:padding="10dp">

                        <ImageView
                            android:id="@+id/icWiFi"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/wifi_info" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="20dp"
                            android:layout_toEndOf="@+id/icWiFi"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="WiFi"
                                    android:textColor="#000000" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="5dp"
                                        android:text="Strength"
                                        android:textColor="#9e9e9e" />

                                    <TextView
                                        android:id="@+id/tv_wifi"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="15%"
                                        android:textColor="#000000" />
                                </LinearLayout>
                            </RelativeLayout>

                            <ProgressBar
                                android:id="@+id/pb_WiFi"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:progress="10"
                                android:progressBackgroundTint="#d9d9d9"
                                android:progressTint="@color/primarymain" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:background="#f3f3f3" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="10dp"
                        android:padding="10dp">

                        <ImageView
                            android:id="@+id/icStorage"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/storage_info" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="20dp"
                            android:layout_toEndOf="@+id/icStorage"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Storage"
                                    android:textColor="#000000" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="5dp"
                                        android:text="Used"
                                        android:textColor="#9e9e9e" />

                                    <TextView
                                        android:id="@+id/tv_storage"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="15%"
                                        android:textColor="#000000" />
                                </LinearLayout>
                            </RelativeLayout>

                            <ProgressBar
                                android:id="@+id/pb_storage"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:progress="10"
                                android:progressBackgroundTint="#d9d9d9"
                                android:progressTint="@color/primarymain" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:background="#f3f3f3" />

                    <RelativeLayout
                        android:id="@+id/RL_Traking"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="10dp"
                        android:padding="10dp">

                        <ImageView
                            android:id="@+id/icImage"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/app_info" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="20dp"
                            android:layout_toEndOf="@+id/icImage"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Applications"
                                    android:textColor="#000000" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="5dp"
                                        android:text="Size"
                                        android:textColor="#9e9e9e" />

                                    <TextView
                                        android:id="@+id/tv_totalsize"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="15%"
                                        android:textColor="#000000" />
                                </LinearLayout>
                            </RelativeLayout>

                            <TextView
                                android:id="@+id/tvtotalapps"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:text="258 App"
                                android:textColor="#a19898" />
                        </LinearLayout>
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#d9d9d9" />
                </LinearLayout>
            </ScrollView>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:layout_above="@+id/RL_TrustEveryone"
            android:background="@drawable/top_shadow_gradient" />

        <RelativeLayout
            android:id="@+id/RL_TrustEveryone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/primarymain"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_trusted"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="13dp"
                    android:background="@color/primarymain"
                    android:gravity="center"
                    android:text="DONE"
                    android:textColor="#ffffff"
                    android:textSize="19sp" />
            </LinearLayout>
        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>
