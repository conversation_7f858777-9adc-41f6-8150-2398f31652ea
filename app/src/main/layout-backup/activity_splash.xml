<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/colorlight"
    android:layout_height="match_parent">

    <RelativeLayout
        android:gravity="center"
        android:layout_above="@+id/bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:layout_centerInParent="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:src="@drawable/logo"
                android:layout_width="180dp"
                android:layout_height="180dp"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="20dp"
                android:fontFamily="@font/regular"
                android:gravity="center"
                android:text="@string/app_name"
                android:textColor="@color/black"
                android:textSize="30sp" />

            <TextView
                android:gravity="center"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:layout_marginLeft="30dp"
                android:textColor="@color/black1"
                android:textSize="17sp"
                android:fontFamily="@font/regular"
                android:text="Who is Tracking My Phone makes it easy to track installed apps permissions like which app track your location, camera permission, storage permission etc."
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/bottom"
        android:gravity="center"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="180dp">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:layout_marginStart="0dp"
            android:layout_marginEnd="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_fileName="loading.json"
            app:lottie_loop="true" />

    </RelativeLayout>

</RelativeLayout>