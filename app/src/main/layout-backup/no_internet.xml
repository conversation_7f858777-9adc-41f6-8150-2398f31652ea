<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="#7d000000">

    <LinearLayout
        android:id="@+id/nointernet"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/roundcornerapp"
        android:layout_margin="10dp"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/img"
            android:layout_marginTop="15dp"
            android:layout_width="85dp"
            android:layout_height="85dp"
            android:src="@drawable/nointernet" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/poppins_medium"
            android:gravity="center"
            android:text="NO INTERNET"
            android:textColor="@color/black"
            android:textSize="20dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginRight="15dp"
            android:layout_marginLeft="15dp"
            android:fontFamily="@font/poppins_regular"
            android:gravity="center"
            android:text="Check your Internet connection and try again."
            android:textColor="@color/black1"
            android:textSize="15dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="7dp"
            android:layout_marginRight="10dp"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="2">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center">

                <androidx.cardview.widget.CardView
                    android:id="@+id/exit"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    app:cardBackgroundColor="#6e7a8e"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp"
                    app:cardUseCompatPadding="true">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/medium"
                        android:gravity="center"
                        android:text="Exit"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center">

                <androidx.cardview.widget.CardView
                    android:id="@+id/refresh"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    app:cardBackgroundColor="#e64a19"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp"
                    app:cardUseCompatPadding="true">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/medium"
                        android:gravity="center"
                        android:text="Refresh"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
