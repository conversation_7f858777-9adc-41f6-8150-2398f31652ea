<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/colorlightdark" >

        <RelativeLayout
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_width="match_parent"
            android:minHeight="100dp"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tondo_bold"
                android:gravity="center"
                android:text="Advertisement"
                android:textColor="@color/black1" />

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/Admob_Small_Native"
            android:visibility="gone"
            android:layout_gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.facebook.ads.NativeAdLayout
            android:id="@+id/native_banner_ad_container"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

    </androidx.cardview.widget.CardView>

</RelativeLayout>