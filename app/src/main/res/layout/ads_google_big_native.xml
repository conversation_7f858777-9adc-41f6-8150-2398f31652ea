<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:orientation="vertical"
        android:background="@drawable/ad_back"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:minHeight="30dp">

        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/lin_as_header"
            android:layout_below="@+id/liear_medi_view"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="2dp">

            <ImageView
                android:layout_gravity="center"
                android:id="@+id/ad_app_icon"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="fitXY"
                android:adjustViewBounds="true" />

            <LinearLayout
                android:gravity="center"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:textSize="7sp"
                        android:textStyle="bold"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:background="@drawable/instgradient"
                        android:padding="3dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text=" AD " />

                    <TextView
                        android:textSize="13sp"
                        android:textColor="@color/adstext"
                        android:gravity="left"
                        android:layout_gravity="left|center_vertical|center_horizontal|center"
                        android:id="@+id/ad_headline"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text=""
                        android:maxLines="1"
                        android:singleLine="true"
                        android:layout_weight="1" />
                </LinearLayout>

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:textSize="10sp"
                        android:textColor="@color/adstext"
                        android:id="@+id/ad_body"
                        android:visibility="visible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="2dp"
                        android:text=""
                        android:maxLines="2"
                        android:layout_weight="1" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:textSize="14sp"
                android:gravity="bottom"
                android:id="@+id/ad_advertiser"
                android:layout_width="wrap_content"
                android:layout_height="match_parent" />

            <RatingBar
                android:id="@+id/ad_stars"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:numStars="5"
                android:stepSize="0.5"
                android:isIndicator="true"
                style="?android:attr/ratingBarStyleSmall" />
        </LinearLayout>

        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/liear_medi_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp">

                <com.google.android.gms.ads.nativead.MediaView
                    android:layout_gravity="center_horizontal"
                    android:id="@+id/ad_media"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="120dp" />
            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/lopo"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/liear_medi_view">

            <LinearLayout
                android:layout_gravity="end"
                android:orientation="horizontal"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:textSize="12sp"
                    android:textColor="#ffececec"
                    android:gravity="left"
                    android:layout_gravity="left|center_vertical|center_horizontal|center"
                    android:id="@+id/ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp" />

                <TextView
                    android:textSize="12sp"
                    android:textColor="#ffececec"
                    android:gravity="left"
                    android:layout_gravity="left|center_vertical|center_horizontal|center"
                    android:id="@+id/ad_store"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp" />
            </LinearLayout>
        </RelativeLayout>

        <Button
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_below="@+id/lin_as_header"
            android:layout_gravity="center"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginBottom="5dp"
            android:background="@drawable/instgradient"
            android:gravity="center"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>
</com.google.android.gms.ads.nativead.NativeAdView>
