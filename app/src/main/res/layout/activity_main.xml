<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:orientation="vertical">

    <LinearLayout
        android:layout_above="@+id/btm1"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Modern Header with Gradient -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:paddingTop="40dp"
            android:paddingBottom="20dp"
            android:elevation="8dp">

            <!-- App Title -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="20dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_vpn_shield"
                    app:tint="@color/text_white"
                    android:layout_marginEnd="12dp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Privacy Guardian"
                        android:textColor="@color/text_white"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Protect Your Digital Life"
                        android:textColor="@color/text_white"
                        android:textSize="12sp"
                        android:alpha="0.9" />

                </LinearLayout>

            </LinearLayout>

            <!-- Modern Menu Button -->
            <androidx.cardview.widget.CardView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="20dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="#40FFFFFF">

                <ImageView
                    android:id="@+id/menu_popup"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/dots"
                    app:tint="@color/text_white" />

            </androidx.cardview.widget.CardView>

        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Modern VPN Hero Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_gradient"
                    android:orientation="vertical"
                    android:paddingTop="30dp"
                    android:paddingBottom="40dp"
                    android:minHeight="400dp">

                    <!-- Hero Title Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:paddingHorizontal="20dp"
                        android:layout_marginBottom="30dp">

                        <!-- Main Shield Icon -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            app:cardCornerRadius="40dp"
                            app:cardElevation="12dp"
                            app:cardBackgroundColor="#40FFFFFF"
                            android:layout_marginBottom="20dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_vpn_shield"
                                app:tint="@color/text_white" />

                        </androidx.cardview.widget.CardView>

                        <!-- Hero Text -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ultimate VPN Protection"
                            android:textColor="@color/text_white"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium"
                            android:gravity="center"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Secure • Private • Anonymous"
                            android:textColor="@color/text_white"
                            android:textSize="16sp"
                            android:alpha="0.9"
                            android:gravity="center"
                            android:letterSpacing="0.1" />

                    </LinearLayout>

                    <!-- Modern VPN Controls Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingHorizontal="20dp"
                        android:paddingBottom="20dp">

                            <!-- Elegant VPN Control Card -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:cardCornerRadius="24dp"
                                app:cardElevation="16dp"
                                app:cardBackgroundColor="@color/background_card"
                                android:layout_marginTop="10dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:padding="28dp"
                                    android:gravity="center">

                                    <!-- Elegant Country Selection -->
                                    <androidx.cardview.widget.CardView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        app:cardCornerRadius="20dp"
                                        app:cardElevation="8dp"
                                        android:layout_marginBottom="24dp"
                                        app:cardBackgroundColor="@color/surface_variant">

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal"
                                            android:gravity="center_vertical"
                                            android:padding="20dp">

                                            <!-- Globe Icon with Background -->
                                            <androidx.cardview.widget.CardView
                                                android:layout_width="48dp"
                                                android:layout_height="48dp"
                                                app:cardCornerRadius="24dp"
                                                app:cardElevation="4dp"
                                                app:cardBackgroundColor="@color/primary_gradient_start"
                                                android:layout_marginEnd="16dp">

                                                <TextView
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_gravity="center"
                                                    android:text="🌍"
                                                    android:textSize="24sp" />

                                            </androidx.cardview.widget.CardView>

                                            <LinearLayout
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:orientation="vertical">

                                                <TextView
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="Choose Server Location"
                                                    android:textSize="14sp"
                                                    android:textColor="@color/text_secondary"
                                                    android:textStyle="bold"
                                                    android:fontFamily="sans-serif-medium" />

                                                <Spinner
                                                    android:id="@+id/spinner_countries"
                                                    android:layout_width="match_parent"
                                                    android:layout_height="44dp"
                                                    android:layout_marginTop="6dp"
                                                    android:background="@android:color/transparent"
                                                    android:textSize="16sp"
                                                    android:textColor="@color/text_primary" />

                                            </LinearLayout>

                                            <ImageView
                                                android:layout_width="20dp"
                                                android:layout_height="20dp"
                                                android:src="@drawable/ic_arrow_drop_down"
                                                app:tint="@color/text_secondary" />

                                        </LinearLayout>

                                    </androidx.cardview.widget.CardView>

                                    <!-- Modern Connection Status -->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:gravity="center"
                                        android:layout_marginBottom="28dp"
                                        android:padding="16dp"
                                        android:background="@drawable/rounded_dialog_background"
                                        android:backgroundTint="@color/background_secondary">

                                        <View
                                            android:id="@+id/status_indicator"
                                            android:layout_width="16dp"
                                            android:layout_height="16dp"
                                            android:background="@drawable/status_indicator_disconnected"
                                            android:layout_marginEnd="12dp"
                                            android:layout_gravity="center_vertical" />

                                        <TextView
                                            android:id="@+id/tv_vpn_connection_status"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Not Connected"
                                            android:textColor="@color/vpn_disconnected"
                                            android:textSize="16sp"
                                            android:textStyle="bold"
                                            android:fontFamily="sans-serif-medium" />

                                    </LinearLayout>

                                    <!-- Premium Secure Button -->
                                    <androidx.cardview.widget.CardView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        app:cardCornerRadius="30dp"
                                        app:cardElevation="12dp"
                                        app:cardBackgroundColor="@android:color/transparent">

                                        <Button
                                            android:id="@+id/btn_secure"
                                            android:layout_width="match_parent"
                                            android:layout_height="64dp"
                                            android:text="🔒 SECURE NOW"
                                            android:textColor="@color/text_white"
                                            android:textSize="18sp"
                                            android:textStyle="bold"
                                            android:background="@drawable/secure_button_gradient"
                                            android:letterSpacing="0.1"
                                            android:fontFamily="sans-serif-medium"
                                            android:stateListAnimator="@null"
                                            android:textAllCaps="false" />

                                    </androidx.cardview.widget.CardView>

                                </LinearLayout>

                            </androidx.cardview.widget.CardView>

                    </LinearLayout>
                </LinearLayout>

                <!-- Privacy Features Section Title -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="30dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Privacy Protection Suite"
                        android:textColor="@color/text_primary"
                        android:textSize="26sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Monitor and protect your digital privacy"
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- Modern Feature Cards Container -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="16dp">

                    <!-- Tracking Detection Card -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/RL_Traking"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/background_card"
                        android:layout_marginBottom="12dp"
                        android:clickable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="20dp">

                            <!-- Icon with Background -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                app:cardCornerRadius="28dp"
                                app:cardElevation="4dp"
                                app:cardBackgroundColor="@color/error_light">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/hm_traking_icon"
                                    app:tint="@color/error_red" />

                            </androidx.cardview.widget.CardView>

                            <!-- Content -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="16dp"
                                android:layout_marginEnd="12dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="App Tracking Detection"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Monitor which apps access your data"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp"
                                    android:layout_marginTop="4dp" />

                            </LinearLayout>

                            <!-- Arrow -->
                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/hm_next_icon"
                                app:tint="@color/text_secondary" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- Network Security Card -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/RL_Network"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/background_card"
                        android:layout_marginBottom="12dp"
                        android:clickable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="20dp">

                            <!-- Icon with Background -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                app:cardCornerRadius="28dp"
                                app:cardElevation="4dp"
                                app:cardBackgroundColor="@color/warning_light">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/hm_network_icon"
                                    app:tint="@color/warning_orange" />

                            </androidx.cardview.widget.CardView>

                            <!-- Content -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="16dp"
                                android:layout_marginEnd="12dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Network Security"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Analyze network connections and security"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp"
                                    android:layout_marginTop="4dp" />

                            </LinearLayout>

                            <!-- Arrow -->
                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/hm_next_icon"
                                app:tint="@color/text_secondary" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- Device Information Card -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/RL_Device_Info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/background_card"
                        android:layout_marginBottom="20dp"
                        android:clickable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="20dp">

                            <!-- Icon with Background -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="56dp"
                                android:layout_height="56dp"
                                app:cardCornerRadius="28dp"
                                app:cardElevation="4dp"
                                app:cardBackgroundColor="@color/success_light">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/hm_device_icon"
                                    app:tint="@color/success_green" />

                            </androidx.cardview.widget.CardView>

                            <!-- Content -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="16dp"
                                android:layout_marginEnd="12dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Device Information"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="View detailed device security status"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp"
                                    android:layout_marginTop="4dp" />

                            </LinearLayout>

                            <!-- Arrow -->
                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/hm_next_icon"
                                app:tint="@color/text_secondary" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>


            </LinearLayout>
        </ScrollView>

    </LinearLayout>

    <!-- Modern Bottom Ad Section -->
    <androidx.cardview.widget.CardView
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        app:cardCornerRadius="0dp"
        app:cardElevation="12dp"
        app:cardBackgroundColor="@color/background_card">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </androidx.cardview.widget.CardView>

</RelativeLayout>

