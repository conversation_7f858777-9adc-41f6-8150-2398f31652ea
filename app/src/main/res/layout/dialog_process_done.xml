<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff"
    android:orientation="vertical"
    android:padding="16dp">

    <View
        android:layout_width="400dp"
        android:layout_height="1dp" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animation_view"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15dp"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/animation" />

    <TextView
        android:id="@+id/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        android:gravity="center"
        android:textColor="#000000"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/btnCompleted"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:background="@color/primarymain"
        android:paddingStart="25dp"
        android:paddingTop="8dp"
        android:paddingEnd="25dp"
        android:paddingBottom="10dp"
        android:text="OK"
        android:textColor="#ffffff"
        android:textSize="18sp" />
</LinearLayout>
