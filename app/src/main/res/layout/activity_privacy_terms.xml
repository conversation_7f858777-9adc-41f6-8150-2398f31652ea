<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="3dp"
            android:src="@drawable/logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingLeft="16dp"
            android:text="Terms of Use"
            android:textColor="@color/black"
            android:textSize="18sp" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/first_check"
        android:layout_below="@+id/top"
        android:layout_margin="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="Posting rules you should be taking care of before you start interaction on our website/application:"
                android:textColor="@color/black"
                android:textSize="12dp"
                android:justificationMode="inter_word"
                android:layout_margin="2dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="Assuring you carry the rights and eligibility to post any form of content you are about to share. We never permit nudism on application/website. We won’t allow posting of sexually explicit content, sexually explicit text, images, or audio content depicting extreme sexual acts such as acts of posting Content that drives traffic to pornographic online services."
                android:textColor="@color/black"
                android:textSize="12dp"
                android:justificationMode="inter_word"
                android:layout_margin="2dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="Please note that we may make exceptions based on artistic, educational, historical, documentary, or scientific nature, or where there are other considerable benefits to the public at large."
                android:textColor="@color/black"
                android:textSize="12dp"
                android:justificationMode="inter_word"
                android:layout_margin="2dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="We don’t permit encouragement or applaud towards terrorism, formed crime scenes or hate association groups on our application/website. Attempting to sell sexual services or weapons and harmful drugs are also not permitted. We will remove reasonable threats of severity, hate arousing content/speech and the intentionally pointing of private individuals. We do not permit attacks/abuse based on skin colour, native, sex, gender, gender identity, sexual orientation, religion, disability or disease."
                android:textColor="@color/black"
                android:textSize="12dp"
                android:justificationMode="inter_word"
                android:layout_margin="2dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="Graphic violence is not allowed and we may remove videos or images of intense, graphic violence to make sure our application/website stays appropriate for everyone."
                android:textColor="@color/black"
                android:textSize="12dp"
                android:justificationMode="inter_word"
                android:layout_margin="2dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="If anyone or any account is spotted doing the above restricted activities they can be booked and arrested for the same. If viewers spot any violations of the above rules they have the right to report the content to us by using the report feature given in our application/website."
                android:textColor="@color/black"
                android:textSize="12dp"
                android:justificationMode="inter_word"
                android:layout_margin="2dp"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <CheckBox
        android:id="@+id/first_check"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/second_check"
        android:layout_marginStart="3dp"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="3dp"
        android:checked="false"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/first_check"
        android:textColor="@color/black"
        android:textSize="12sp" />

    <CheckBox
        android:id="@+id/second_check"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/termtextview"
        android:layout_marginStart="3dp"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="3dp"
        android:checked="false"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/second_check"
        android:textColor="@color/black"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/termtextview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom"
        android:gravity="center"
        android:paddingStart="12dp"
        android:paddingTop="16dp"
        android:paddingEnd="12dp"
        android:paddingBottom="16dp"
        android:text="@string/clickforprivacy"
        android:textColor="@color/black"
        android:textSize="12sp" />

    <LinearLayout
        android:id="@+id/bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal">

        <Button
            android:id="@+id/accept_button"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/ratebox"
            android:gravity="center"
            android:text="Get Started"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="16sp" />
    </LinearLayout>
</RelativeLayout>