<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_overlay">

    <!-- Modern Ad Dialog Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="24dp"
        app:cardCornerRadius="24dp"
        app:cardElevation="16dp"
        app:cardBackgroundColor="@color/background_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="28dp">

            <!-- Modern Ad Title -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="20dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@color/primary_gradient_start"
                    android:layout_marginEnd="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="🔒"
                        android:textSize="24sp" />

                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Securing Connection"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Please wait while we prepare your VPN"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

        <!-- Ad Content Area -->
        <FrameLayout
            android:id="@+id/ad_container"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:background="#f5f5f5"
            android:layout_marginBottom="15dp">

            <!-- Placeholder for actual ad -->
            <LinearLayout
                android:id="@+id/ad_placeholder"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:background="#e0e0e0">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/logo"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Loading Advertisement..."
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <ProgressBar
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    style="?android:attr/progressBarStyleSmall" />

            </LinearLayout>

        </FrameLayout>

        <!-- Timer and Skip Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Connecting to secure server..."
                android:textColor="#666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5s"
                android:textColor="#ff6b35"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/timer_background"
                android:padding="8dp"
                android:minWidth="40dp"
                android:gravity="center" />

        </LinearLayout>

            <!-- Skip Button -->
            <Button
                android:id="@+id/btn_skip"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:text="Skip (5s)"
                android:textColor="#ffffff"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/skip_button_background"
                android:enabled="false"
                android:alpha="0.5" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>
