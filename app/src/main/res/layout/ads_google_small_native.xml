<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:orientation="vertical"
        android:padding="3dp"
        android:background="@drawable/ad_back"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.cardview.widget.CardView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="1dp"
                    android:layout_marginRight="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="1dp">

                    <ImageView
                        android:layout_gravity="center"
                        android:visibility="visible"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <com.google.android.gms.ads.nativead.MediaView
                        android:layout_gravity="center"
                        android:id="@+id/ad_media"
                        android:visibility="visible"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </androidx.cardview.widget.CardView>

                <RelativeLayout
                    android:orientation="vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="100dp"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1">

                    <RelativeLayout
                        android:paddingTop="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="60dp">

                        <LinearLayout
                            android:orientation="horizontal"
                            android:id="@+id/lin1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginRight="10dp">

                            <TextView
                                android:textSize="8sp"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:gravity="center"
                                android:background="@drawable/instgradient"
                                android:padding="1dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=" AD " />

                            <TextView
                                android:textSize="13sp"
                                android:layout_marginLeft="5dp"
                                android:textColor="@color/adstext"
                                android:gravity="top|left|center_vertical|center_horizontal|center"
                                android:layout_gravity="center"
                                android:id="@+id/ad_headline"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:text=""
                                android:maxLines="1" />
                        </LinearLayout>

                        <LinearLayout
                            android:gravity="center_vertical"
                            android:orientation="vertical"
                            android:id="@+id/lin2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginRight="10dp"
                            android:layout_below="@+id/lin1">

                            <TextView
                                android:textSize="10sp"
                                android:textColor="@color/adstext"
                                android:gravity="left|center_vertical|center_horizontal|center"
                                android:layout_gravity="center"
                                android:id="@+id/ad_body"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:text=""
                                android:maxLines="2"
                                android:layout_weight="1" />

                        </LinearLayout>

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_alignParentBottom="true"
                        android:layout_width="match_parent"
                        android:layout_height="40dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/lin2">

                            <Button
                                android:id="@+id/ad_call_to_action"
                                android:layout_width="match_parent"
                                android:layout_height="34dp"
                                android:layout_gravity="center"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="5dp"
                                android:layout_marginTop="5dp"
                                android:background="@drawable/instgradient"
                                android:gravity="center"
                                android:text=""
                                android:textColor="@color/white"
                                android:textSize="12sp" />
                        </RelativeLayout>

                    </RelativeLayout>

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp" />

                <ImageView
                    android:paddingTop="5dp"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:maxHeight="150dp" />
            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="0dp">

            <ImageView
                android:layout_gravity="center"
                android:id="@+id/ad_app_icon"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="fitXY"
                android:adjustViewBounds="true" />

            <TextView
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="bottom"
                android:id="@+id/ad_advertiser"
                android:layout_width="wrap_content"
                android:layout_height="match_parent" />

            <RatingBar
                android:id="@+id/ad_stars"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:numStars="5"
                android:stepSize="0.5"
                android:isIndicator="true"
                style="?android:attr/ratingBarStyleSmall" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:minHeight="30dp">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp" />

        <LinearLayout
            android:orientation="horizontal"
            android:id="@+id/lin_as_header"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="2dp">

            <LinearLayout
                android:gravity="center"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/liear_medi_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lin_as_header" />

        <RelativeLayout
            android:id="@+id/lopo"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/liear_medi_view">

            <LinearLayout
                android:layout_gravity="end"
                android:orientation="horizontal"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:gravity="left"
                    android:layout_gravity="left|center_vertical|center_horizontal|center"
                    android:id="@+id/ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp" />

                <TextView
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:gravity="left"
                    android:layout_gravity="left|center_vertical|center_horizontal|center"
                    android:id="@+id/ad_store"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp" />
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
