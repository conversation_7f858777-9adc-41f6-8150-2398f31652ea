<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        app:cardBackgroundColor="@color/colorlightdark"
        app:cardCornerRadius="5dp">

        <RelativeLayout
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_width="match_parent"
            android:layout_height="250dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/tondo_bold"
                android:gravity="center"
                android:text="Advertisement"
                android:textColor="@color/black1" />

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/Admob_Native_Frame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="Ad loading..."
                android:textColor="@color/white"
                android:visibility="gone" />
        </FrameLayout>

        <com.facebook.ads.NativeAdLayout
            android:id="@+id/native_ad_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone" />

        <FrameLayout
            android:visibility="gone"
            android:background="@color/white"
            android:id="@+id/max_native_ad_layout"
            android:layout_width="match_parent"
            android:layout_height="320dp" />

    </androidx.cardview.widget.CardView>

</RelativeLayout>