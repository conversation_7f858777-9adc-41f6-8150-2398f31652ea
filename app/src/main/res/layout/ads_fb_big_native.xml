<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@color/colorlightdark"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingBottom="7dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="3dp">

    <LinearLayout
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:textSize="16sp"
            android:textColor="@color/adstext"
            android:ellipsize="end"
            android:id="@+id/native_ad_sponsored_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lines="1"
            android:layout_weight="1"
            android:fontFamily="sans-serif-smallcaps" />

        <LinearLayout
            android:gravity="end"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:id="@+id/ad_choices_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <com.facebook.ads.MediaView
        android:gravity="center"
        android:id="@+id/native_ad_media"
        android:layout_width="match_parent"
        android:layout_height="120dp" />

    <LinearLayout
        android:orientation="horizontal"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.facebook.ads.MediaView
            android:layout_gravity="center"
            android:id="@+id/native_ad_icon"
            android:layout_width="50dp"
            android:layout_height="50dp" />

        <LinearLayout
            android:layout_gravity="center"
            android:orientation="vertical"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_weight="1">

            <TextView
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@color/adstext"
                android:ellipsize="end"
                android:id="@+id/native_ad_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lines="1"
                android:fontFamily="sans-serif-condensed" />

            <TextView
                android:textSize="9sp"
                android:textColor="@color/adstext"
                android:ellipsize="end"
                android:id="@+id/native_ad_social_context"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lines="1"
                android:fontFamily="sans-serif-condensed" />

            <TextView
                android:textSize="9sp"
                android:textColor="@color/adstext"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:id="@+id/native_ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lines="1"
                android:singleLine="true"
                android:fontFamily="sans-serif-condensed" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:textSize="15sp"
            android:textColor="@android:color/white"
            android:layout_gravity="center_vertical"
            android:id="@+id/native_ad_call_to_action"
            android:background="@drawable/instgradient"
            android:paddingLeft="3dp"
            android:paddingRight="3dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:fontFamily="sans-serif-condensed" />
    </LinearLayout>
</LinearLayout>
