<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#02000000"/>
            <corners android:radius="16dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#03000000"/>
            <corners android:radius="15dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#04000000"/>
            <corners android:radius="14dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#05000000"/>
            <corners android:radius="15dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#06000000"/>
            <corners android:radius="12dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#07000000"/>
            <corners android:radius="11dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <padding android:left="1dp" android:top="1dp" android:right="1dp" android:bottom="1dp"/>
            <stroke android:width="1dp" android:color="#08000000"/>
            <corners android:radius="10dp"/>
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/colorlight"/>
            <corners android:radius="10dp"/>
        </shape>
    </item>
</layer-list>
