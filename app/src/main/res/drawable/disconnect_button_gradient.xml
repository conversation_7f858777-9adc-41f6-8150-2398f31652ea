<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#E53935"
                android:endColor="#C62828"
                android:angle="135" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#CCCCCC"
                android:endColor="#999999"
                android:angle="135" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#F44336"
                android:endColor="#D32F2F"
                android:angle="135" />
            <corners android:radius="25dp" />
        </shape>
    </item>
</selector>
