<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/gradient_purple_start"
                android:endColor="@color/gradient_purple_end"
                android:angle="135" />
            <corners android:radius="30dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/text_secondary"
                android:endColor="@color/divider_color"
                android:angle="135" />
            <corners android:radius="30dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/gradient_blue_start"
                android:endColor="@color/gradient_blue_end"
                android:angle="135" />
            <corners android:radius="30dp" />
        </shape>
    </item>
</selector>
