<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_5sdp"
        app:cardCornerRadius="10dp"
        app:cardElevation="20dp">

        <RelativeLayout
            android:id="@+id/flag_image_r"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="6dp">

            <ImageView
                android:id="@+id/country_flag"
                android:layout_width="60dp"
                android:layout_height="50dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:src="@drawable/flag_default" />

            <TextView
                android:id="@+id/region_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:layout_toRightOf="@+id/country_flag"
                android:text="DEFAULT REGION"
                android:textColor="@color/primary"
                android:textSize="18dp" />

            <TextView
                android:id="@+id/region_limit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="20dp"
                android:text="218ms"
                android:textSize="16dp"
                android:visibility="gone" />


            <pl.droidsonroids.gif.GifImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/upgrade" />


        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="1dp"
            android:paddingLeft="5dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/country_flag1"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:scaleType="fitXY"
                app:srcCompat="@drawable/flag_default" />

            <TextView
                android:id="@+id/region_title1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="20dp"
                android:paddingLeft="20dp"
                android:text="DEFAULT REGION"
                android:textColor="@android:color/white"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/region_limit1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:padding="@dimen/default_padding"
                android:textColor="@android:color/white"
                android:textSize="15dp"
                android:visibility="visible" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>
</RelativeLayout>