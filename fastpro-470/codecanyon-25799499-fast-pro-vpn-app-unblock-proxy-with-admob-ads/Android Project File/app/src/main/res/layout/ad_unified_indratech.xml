<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorPrimaryDark">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardUseCompatPadding="true"
        app:cardElevation="10dp"
        >

        <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="#FFFFFF"
            android:minHeight="50dp"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="20dp"
                android:paddingTop="3dp"
                android:paddingRight="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ad_app_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:adjustViewBounds="true"
                        android:paddingEnd="5dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="5dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/ad_headline"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#0000FF"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/ad_advertiser"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="bottom"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <RatingBar
                                android:id="@+id/ad_stars"
                                style="?android:attr/ratingBarStyleSmall"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:isIndicator="true"
                                android:numStars="5"
                                android:stepSize="0.5" />
                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/ad_body"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="20dp"
                        android:layout_marginRight="20dp"
                        android:textSize="12sp" />

                    <com.google.android.gms.ads.nativead.MediaView
                        android:id="@+id/ad_media"
                        android:layout_width="250dp"
                        android:layout_height="175dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="5dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:orientation="horizontal"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp">

                        <TextView
                            android:id="@+id/ad_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingStart="5dp"
                            android:paddingLeft="5dp"
                            android:paddingEnd="5dp"
                            android:paddingRight="5dp"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/ad_store"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingStart="5dp"
                            android:paddingLeft="5dp"
                            android:paddingEnd="5dp"
                            android:paddingRight="5dp"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/ad_call_to_action"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textAlignment="center"
                            android:textSize="12sp"
                            android:background="@color/colorPrimary"
                            android:textColor="@color/white"
                            />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

    </androidx.cardview.widget.CardView>


</com.google.android.gms.ads.nativead.NativeAdView>