<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    tools:context="com.inapp.vpn.Activities.ActivityMain"
    xmlns:app="http://schemas.android.com/apk/res-auto">



    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:background="#8150F9"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:visibility="gone"
                android:contentDescription="@string/app_name"
                app:srcCompat="@drawable/ic_menu" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="0dp"
                    android:background="@color/white"
                    android:padding="10dp">


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center"
                        android:layout_marginStart="20dp"
                        android:layout_marginBottom="0dp"
                        android:text="@string/app_name"
                        android:textColor="@color/colorPrimary"
                        android:textSize="24dp"
                        android:textStyle="bold" />


                    <LinearLayout
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <pl.droidsonroids.gif.GifImageView
                            android:id="@+id/ic_crown"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:adjustViewBounds="true"
                            android:background="@drawable/upgrade" />

                    </LinearLayout>




                    <LinearLayout
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_height="50dp"
                        android:layout_marginEnd="60dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">


                        <ImageView
                            android:id="@+id/rate_us"
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:src="@drawable/star"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_height="50dp"
                        android:layout_marginEnd="110dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">


                        <ImageView
                            android:id="@+id/term_link"
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:backgroundTint="@color/primary"
                            android:background="@drawable/privacy"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_height="50dp"
                        android:layout_marginEnd="160dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">


                        <ImageView
                            android:id="@+id/play_link"
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:src="@drawable/share"/>
                    </LinearLayout>

                </RelativeLayout>

            </LinearLayout>


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_alignParentTop="true"
                android:layout_marginTop="0dp"
                android:background="@drawable/h_bg"
                android:padding="10dp"/>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:layout_marginTop="-90dp"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_20sdp">


                        <LinearLayout
                            android:layout_width="190dp"
                            android:layout_height="220dp"
                            android:layout_marginStart="10dp"
                            android:layout_marginTop="1dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.cardview.widget.CardView
                                android:layout_width="wrap_content"
                                android:layout_height="190dp"
                                android:layout_gravity="center"
                                android:layout_marginLeft="0dp"
                                android:layout_marginTop="2dp"
                                android:layout_marginRight="0dp"
                                app:cardCornerRadius="10dp"
                                app:cardElevation="10dp"
                                app:cardUseCompatPadding="true">

                                <LinearLayout
                                    android:layout_width="135dp"
                                    android:layout_height="178dp"
                                    android:layout_marginTop="-5dp"
                                    android:layout_marginRight="0dp"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/downloading"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginBottom="0dp"
                                        android:text="3.4 mbit"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="@dimen/_15sdp" />


                                    <pl.droidsonroids.gif.GifImageView
                                        android:layout_width="100dp"
                                        android:layout_height="80dp"
                                        android:layout_gravity="center"
                                        android:src="@drawable/up_i" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginBottom="10dp"
                                        android:text="Download"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="@dimen/_15sdp"
                                        android:textStyle="bold">

                                    </TextView>


                                </LinearLayout>

                            </androidx.cardview.widget.CardView>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="190dp"
                            android:layout_height="220dp"
                            android:layout_marginStart="10dp"
                            android:layout_marginTop="1dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.cardview.widget.CardView
                                android:layout_width="wrap_content"
                                android:layout_height="190dp"
                                android:layout_gravity="center"
                                android:layout_marginLeft="0dp"
                                android:layout_marginTop="2dp"
                                android:layout_marginRight="0dp"
                                android:layout_marginBottom="5dp"
                                app:cardCornerRadius="10dp"
                                app:cardElevation="10dp"
                                app:cardUseCompatPadding="true">

                                <LinearLayout
                                    android:layout_width="135dp"
                                    android:layout_height="178dp"
                                    android:layout_marginTop="-5dp"
                                    android:layout_marginRight="0dp"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/uploading"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="3.4 mbit"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="@dimen/_15sdp" />

                                    <pl.droidsonroids.gif.GifImageView
                                        android:layout_width="100dp"
                                        android:layout_height="80dp"
                                        android:src="@drawable/up_i" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginBottom="10dp"
                                        android:text="Upload"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="@dimen/_15sdp"
                                        android:textStyle="bold">

                                    </TextView>


                                </LinearLayout>


                            </androidx.cardview.widget.CardView>


                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="200dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">



                        <androidx.cardview.widget.CardView
                            android:layout_width="wrap_content"
                            android:layout_height="90dp"
                            android:layout_gravity="start"
                            app:cardCornerRadius="10dp"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="10dp"
                            app:cardElevation="10dp"
                            android:layout_marginRight="20dp"
                            android:layout_marginBottom="0dp"
                            app:cardUseCompatPadding="true">


                            <pl.droidsonroids.gif.GifImageView
                                android:id="@+id/gifImageView1"
                                android:layout_width="100dp"
                                android:layout_height="80dp"
                                android:adjustViewBounds="true"
                                android:visibility="gone"
                                android:layout_toEndOf="@+id/ic_crown"
                                android:layout_toRightOf="@+id/ic_crown"
                                android:src="@drawable/up_i"
                                android:background="@color/transparent" />


                            <com.airbnb.lottie.LottieAnimationView
                                android:id="@+id/animation_view"
                                android:layout_width="120dp"
                                android:layout_height="70dp"
                                android:layout_marginStart="30dp"
                                android:layout_marginTop="35dp"
                                android:layout_marginBottom="0dp"
                                android:visibility="gone"
                                app:lottie_autoPlay="true"
                                app:lottie_loop="true"
                                app:lottie_rawRes="@raw/homel" />

                            <pl.droidsonroids.gif.GifImageView
                                android:id="@+id/gifImageView2"
                                android:layout_width="100dp"
                                android:layout_height="80dp"
                                android:layout_gravity="center"
                                android:visibility="gone"
                                android:adjustViewBounds="true"
                                android:src="@drawable/up_i"
                                android:background="@color/transparent" />
                            <TextView
                                android:id="@+id/tv_timer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="12dp"
                                android:gravity="center"
                                android:hint="00:00:00"
                                android:visibility="gone"
                                android:textColor="@color/textcolor"
                                android:textColorHint="@color/colorPrimary"
                                android:textSize="15sp" />

                            <TextView
                                android:id="@+id/connection_state"
                                android:layout_width="wrap_content"
                                android:layout_height="33dp"
                                android:layout_marginStart="20dp"
                                android:layout_marginTop="5dp"
                                android:layout_gravity="start"
                                android:layout_marginEnd="20dp"
                                android:layout_marginBottom="0dp"
                                android:drawableStart="@drawable/signal"
                                android:drawableLeft="@drawable/signal"
                                android:drawableEnd="@drawable/signal"
                                android:drawableRight="@drawable/signal"
                                android:drawablePadding="10dp"
                                android:gravity="center"
                                android:text="Connected Server"
                                android:textColor="@color/colorPrimary"
                                android:textStyle="bold" />

                        </androidx.cardview.widget.CardView>


                        <androidx.cardview.widget.CardView
                            android:layout_width="90dp"
                            android:layout_height="90dp"
                            android:layout_gravity="end"
                            android:layout_marginTop="-88dp"
                            android:layout_marginEnd="30dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginBottom="5dp"
                            app:cardCornerRadius="18dp"
                            app:cardElevation="5dp"
                            app:cardUseCompatPadding="true">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/connect_btn"
                                    android:layout_width="55dp"
                                    android:layout_height="65dp"
                                    android:layout_alignParentBottom="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginBottom="2dp"
                                    android:src="@drawable/play" />


                            </LinearLayout>


                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:id="@+id/status_time_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3.4 mbit"
                            android:textColor="@color/colorPrimaryDark"
                            android:textSize="@dimen/_15sdp"
                            android:visibility="gone" />

                    </LinearLayout>

                </RelativeLayout>


            </LinearLayout>





            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="1dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="110dp"
                    android:layout_gravity="center"
                    app:cardCornerRadius="20dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="-15dp"
                    app:cardElevation="15dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="5dp"
                    app:cardUseCompatPadding="true">

                    <LinearLayout
                        android:id="@+id/laySelectServer"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_50sdp"
                        android:layout_margin="5dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_marginRight="45dp"
                            android:layout_gravity="start"
                            android:layout_alignParentStart="true"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="5dp"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:id="@+id/flag_image"
                                android:layout_width="35dp"
                                android:layout_marginTop="5dp"
                                android:layout_height="35dp"
                                android:src="@drawable/internet" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="20dp"
                            android:layout_marginStart="20dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/flag_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_margin="5dp"
                                android:text="Select Country"
                                android:textColor="@color/colorPrimary"
                                android:textSize="22dp">

                            </TextView>

                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/vpn_details"
                            android:layout_width="50dp"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:background="?selectableItemBackground"
                            android:layout_marginEnd="20dp"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView

                                android:layout_width="20dp"
                                android:layout_marginTop="0dp"
                                android:layout_height="20dp"
                                android:src="@drawable/drop_down"/>
                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>
                <androidx.cardview.widget.CardView
                    android:layout_width="250dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="20dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="10dp">


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center">


                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="5dp"
                            android:background="@drawable/ic_locations" />

                        <TextView
                            android:id="@+id/tv_ip_address"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="5dp"
                            android:gravity="center"
                            android:text="Your IP Address"
                            android:textColor="@color/gnt_black"
                            android:textSize="18dp" />


                    </LinearLayout>


                </androidx.cardview.widget.CardView>

                <RelativeLayout
                    android:id="@+id/footer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/header"
                    android:layout_marginTop="10dp"
                    android:visibility="gone">

                    <View
                        android:id="@+id/saperater"
                        android:layout_width="2dp"
                        android:layout_height="30dp"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="6dp"
                        android:background="#000" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@+id/saperater"
                        android:gravity="center"
                        android:orientation="vertical">


                        <ImageView
                            android:layout_width="100dp"
                            android:layout_height="20dp"
                            android:src="@drawable/status_arrow" />


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/connection_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentLeft="true"
                                android:layout_centerVertical="true"
                                android:gravity="center"
                                android:text="Selected"
                                android:textColor="#000"
                                android:textSize="22dp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/connection_status_image"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_gravity="center"
                                android:layout_marginLeft="10dp"
                                android:gravity="center"
                                android:visibility="gone" />
                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_toRightOf="@+id/saperater"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="120dp"
                            android:layout_height="20dp"
                            android:src="@drawable/status_arrow" />

                        <TextView
                            android:id="@+id/purchase_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:gravity="center"
                            android:text="Go Premium"
                            android:textColor="#000"
                            android:textSize="18dp"
                            android:textStyle="bold" />
                    </LinearLayout>

                </RelativeLayout>

                <ProgressBar
                    android:id="@+id/connection_progress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="230dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:progressDrawable="@drawable/progress"
                    android:progressTint="@color/colorPrimary"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/banner_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"></LinearLayout>


            <RelativeLayout
                android:id="@+id/fl_adplaceholder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="-8dp"
                android:visibility="visible" />

            <com.facebook.ads.NativeAdLayout
                android:id="@+id/native_ad_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="-8dp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcv_free"
                android:layout_width="match_parent"
                android:layout_height="245dp"
                android:layout_marginTop="10dp"
                android:visibility="gone" />

            <FrameLayout
                android:id="@+id/adPlaceHolderFrameLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginRight="5dp"
                android:visibility="visible" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="horizontal">







            </LinearLayout>






        </LinearLayout>
    </androidx.core.widget.NestedScrollView>





</layout>

