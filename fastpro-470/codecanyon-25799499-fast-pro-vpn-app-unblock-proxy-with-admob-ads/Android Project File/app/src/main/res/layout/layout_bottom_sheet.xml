<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bsContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <Button
        android:id="@+id/but_subs"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="104dp"
        android:layout_marginTop="40dp"
        android:background="@drawable/one_month"
        android:backgroundTint="@color/primary"
        android:text="   Buy SUBSCRIPTION   "
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_marginTop="104dp"
        android:backgroundTint="@color/white"
        android:text="Watch the Video And Unlock Premium Servers"
        android:textColor="@color/gnt_black"
        android:textSize="14dp"
        android:textStyle="italic"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />






</androidx.constraintlayout.widget.ConstraintLayout>