<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    >


    <androidx.cardview.widget.CardView
        android:layout_margin="@dimen/_5sdp"
        android:layout_width="match_parent"
        app:cardCornerRadius="10dp"
        android:layout_height="wrap_content"
        app:cardElevation="20dp">

    <RelativeLayout
        android:layout_centerInParent="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="6dp"
        android:id="@+id/flag_image_r"
        >
        <ImageView

            android:src="@drawable/flag_default"
            android:layout_centerVertical="true"
            android:id="@+id/country_flag"
            android:layout_marginRight="15dp"
            android:layout_width="60dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:scaleType="fitXY"
            app:srcCompat="@drawable/flag_default"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:id="@+id/region_title"
            android:layout_toRightOf="@+id/country_flag"
            android:textSize="18dp"
            android:layout_marginLeft="20dp"
            android:textColor="@color/colorPrimary"
            android:text="DEFAULT REGION"

            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:text="218ms"
            android:visibility="gone"
            android:textSize="16dp"
            android:layout_marginRight="20dp"
            />
        <ImageView
            android:id="@+id/region_limit"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_weight="0.5"
            android:src="@drawable/server_signal_3"
            android:visibility="visible" />

    </RelativeLayout>


    <LinearLayout
        android:padding="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        android:paddingLeft="5dp">

        <ImageView
            android:id="@+id/country_flag1"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:scaleType="fitXY"
            app:srcCompat="@drawable/flag_default" />

        <TextView
            android:id="@+id/region_title1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="20dp"
            android:paddingLeft="20dp"
            android:text="DEFAULT REGION"
            android:textColor="@android:color/white"
            android:textSize="12dp" />

        <ImageView
            android:id="@+id/region_limit1"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:layout_weight="0.5"
            android:paddingRight="10dp"
            android:scaleType="fitEnd"
            android:src="@drawable/server_signal_3"
            android:visibility="visible" />
    </LinearLayout>


</androidx.cardview.widget.CardView>
</RelativeLayout>