<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginLeft="15dp"
    android:layout_marginRight="15dp"
    android:layout_marginTop="10dp"
    android:background="@color/gnt_black"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white">

        <com.facebook.ads.AdIconView
            android:id="@+id/native_icon_view"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:gravity="center" />

        <RelativeLayout
            android:id="@+id/ad_choices_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="2dp"
            android:gravity="bottom"
            android:layout_alignBottom="@id/native_icon_view"
            />

        <LinearLayout
            android:id="@+id/contentlayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical"
            android:layout_toRightOf="@id/native_icon_view"
            android:layout_toLeftOf="@id/native_ad_call_to_action"
            >

            <TextView
                android:id="@+id/native_ad_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textAlignment="textStart"
                android:gravity="start"
                android:layout_marginLeft="5dp"
                />

            <TextView
                android:id="@+id/native_ad_social_context"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:textSize="12sp"
                android:textAlignment="textStart"
                android:gravity="start"
                android:layout_marginLeft="5dp"
                />



            <TextView
                android:id="@+id/native_ad_sponsored_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:lines="1"
                android:padding="2dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="12sp" />

        </LinearLayout>

        <Button
            android:id="@+id/native_ad_call_to_action"
            android:layout_width="80dp"
            android:layout_height="50dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:background="#4286F4"
            android:gravity="center"
            android:paddingLeft="3dp"
            android:paddingRight="3dp"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:layout_marginRight="5dp"
            android:layout_centerInParent="true"
            android:layout_marginLeft="5dp"
            />

    </RelativeLayout>


</LinearLayout>