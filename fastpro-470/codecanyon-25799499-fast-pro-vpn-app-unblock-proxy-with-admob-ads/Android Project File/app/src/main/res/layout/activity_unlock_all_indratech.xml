<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:weightSum="1">

    <View
        android:id="@+id/tv_close"
        android:layout_width="match_parent"
        android:layout_height="1700dp"
        android:layout_weight="1"
        android:visibility="visible"/>

    <LinearLayout
        android:id="@+id/sel_dialog"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/primary"
            android:gravity="center"
            android:padding="10dp"
            android:text="Get Premium Today"
            android:visibility="visible"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="20dp" />


        <View
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:background="@drawable/round_h"
            android:orientation="vertical"
            android:weightSum="1">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/crown"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="Get Premium Today"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:textSize="@dimen/_20sdp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:text="Remove Ads and Enjoy Better Experince"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp"
                android:layout_marginRight="0dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text=" \u2022 No Ads"
                android:layout_marginStart="120dp"
                android:layout_gravity="start"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="120dp"
                android:layout_marginTop="10dp"
                android:text="\u2022 Increase Your Connection Speed"
                android:textColor="@color/white"
                android:layout_gravity="start"
                android:textSize="@dimen/_12sdp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_gravity="start"
                android:layout_marginStart="120dp"
                android:text="\u2022 Protect Your Security"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />


            <androidx.cardview.widget.CardView

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:cardCornerRadius="15dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@color/white"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:layout_marginBottom="@dimen/activity_vertical_margin">


                <LinearLayout

                    android:layout_width="382dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:weightSum="1">


                    <RadioButton
                        android:id="@+id/one_month"
                        android:layout_width="32dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="30dp"
                        android:button="@drawable/bg_checkbox"
                        android:gravity="center" />


                    <LinearLayout
                        android:layout_width="143dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">


                        <TextView
                            android:layout_width="126dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/n1"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/_18sdp" />
                    </LinearLayout>




                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:elevation="10dp"
                        android:layout_margin="5dp"
                        android:background="@drawable/btn_pr"
                        android:layout_marginEnd="10dp"
                        android:orientation="vertical"
                        android:padding="5dp">



                        <TextView

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/tech_one_month"
                            android:textColor="@color/gnt_white"
                            android:textSize="@dimen/default_text_size" />
                    </LinearLayout>



                </LinearLayout>
            </androidx.cardview.widget.CardView>
            <androidx.cardview.widget.CardView

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:cardCornerRadius="15dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@color/white"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:layout_marginBottom="@dimen/activity_vertical_margin">


                <LinearLayout

                    android:layout_width="382dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:weightSum="1">


                    <RadioButton
                        android:id="@+id/three_month"
                        android:layout_width="32dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="30dp"
                        android:button="@drawable/bg_checkbox"
                        android:gravity="center" />


                    <LinearLayout
                        android:layout_width="143dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/n2"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/_18sdp" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="5dp"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/btn_pr"
                        android:elevation="10dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="5dp">


                        <TextView

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/tech_three_months"
                            android:textColor="@color/gnt_white"
                            android:textSize="@dimen/default_text_size" />
                    </LinearLayout>



                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <androidx.cardview.widget.CardView

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:cardCornerRadius="15dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@color/white"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:layout_marginBottom="@dimen/activity_vertical_margin">


                <LinearLayout

                    android:layout_width="382dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"

                    android:weightSum="1">


                    <RadioButton
                        android:id="@+id/six_month"
                        android:layout_width="32dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="30dp"
                        android:button="@drawable/bg_checkbox"
                        android:gravity="center" />


                    <LinearLayout
                        android:layout_width="143dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/n3"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/_18sdp" />
                    </LinearLayout>




                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:elevation="10dp"
                        android:layout_margin="5dp"
                        android:background="@drawable/btn_pr"
                        android:layout_marginEnd="10dp"
                        android:orientation="vertical"
                        android:padding="5dp">



                        <TextView

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/tech_six_months"
                            android:textColor="@color/gnt_white"
                            android:textSize="@dimen/default_text_size" />
                    </LinearLayout>



                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:cardCornerRadius="15dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@color/white"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:layout_marginBottom="@dimen/activity_vertical_margin">


                <LinearLayout
                    android:id="@+id/ll_sub_plan"
                    android:layout_width="382dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:weightSum="1">


                    <RadioButton
                        android:id="@+id/one_year"
                        android:layout_width="32dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="30dp"
                        android:button="@drawable/bg_checkbox"
                        android:gravity="center" />


                    <LinearLayout
                        android:layout_width="143dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/n4"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/_18sdp" />
                    </LinearLayout>




                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:elevation="10dp"
                        android:layout_margin="5dp"
                        android:background="@drawable/btn_pr"
                        android:layout_marginEnd="10dp"
                        android:orientation="vertical"
                        android:padding="5dp">



                        <TextView

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/tech_twelve_months"
                            android:textColor="@color/gnt_white"
                            android:textSize="@dimen/default_text_size" />
                    </LinearLayout>



                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <Button
                android:id="@+id/all_pur"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/one_month"
                android:text="     Buy Now     "
                android:textColor="@color/white"
                android:textSize="17dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Recuring Billing, Cancel Anytime on Google Play Store"
                android:textSize="15dp"
                android:layout_margin="2dp"
                android:layout_marginBottom="5dp"
                android:textColor="@color/white"/>






        </LinearLayout>


    </LinearLayout>
</LinearLayout>
