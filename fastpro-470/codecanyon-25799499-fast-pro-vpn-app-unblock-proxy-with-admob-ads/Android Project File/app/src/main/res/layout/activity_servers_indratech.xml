<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/toolbarold"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:titleTextColor="@color/white"
        android:gravity="center_vertical"
        />
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_below="@id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    </androidx.viewpager.widget.ViewPager>
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_below="@+id/toolbarold"
        android:layout_height="wrap_content"
        android:minHeight="?actionBarSize"
        app:tabGravity="fill"
        app:tabIndicatorColor="@color/textcolor"
        app:tabIndicatorHeight="4dp"
        app:tabSelectedTextColor="#000"
        app:tabMode="fixed"
        >
    </com.google.android.material.tabs.TabLayout>


</RelativeLayout>