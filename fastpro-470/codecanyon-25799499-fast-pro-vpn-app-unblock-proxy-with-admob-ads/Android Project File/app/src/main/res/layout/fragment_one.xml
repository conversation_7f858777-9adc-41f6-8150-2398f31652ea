<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <RelativeLayout
        android:id="@+id/animation_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        >

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loading_view"
            android:layout_width="200dp"
            android:layout_height="120dp"
            android:layout_marginTop="-5dp"
            android:visibility="visible"
            android:layout_marginBottom="-5dp"
            android:layout_centerHorizontal="true"
            app:lottie_rawRes="@raw/homel"
            app:lottie_loop="true"
            android:layout_below="@+id/connect_btn"
            app:lottie_autoPlay="true"
            android:layout_centerInParent="true"
            />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/region_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <RelativeLayout
        android:id="@+id/purchase_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/trans_layer"
        android:visibility="visible"
        android:clickable="true"
        android:focusable="true"
        >

        <ImageButton
            android:id="@+id/vip_unblock"
            android:layout_width="400dp"
            android:layout_height="800dp"
            android:layout_centerInParent="true"
            android:padding="65dp"
            android:background="@drawable/trans"
            android:adjustViewBounds="true"/>


        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/vip_unblock"
            android:text="Unlock"
            android:textSize="22dp"
            android:textAlignment="center"
            android:textStyle="bold"
            android:visibility="gone"
            android:textColor="@color/colorPrimary"
            />

    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>