<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/animation_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        >

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loading_view"
            android:layout_width="200dp"
            android:layout_height="120dp"
            android:layout_marginTop="-5dp"
            android:visibility="visible"
            android:layout_marginBottom="-5dp"
            android:layout_centerHorizontal="true"
            app:lottie_rawRes="@raw/homel"
            app:lottie_loop="true"
            app:lottie_autoPlay="true"
            android:layout_centerInParent="true"
            />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/region_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/lytAd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/lytAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>