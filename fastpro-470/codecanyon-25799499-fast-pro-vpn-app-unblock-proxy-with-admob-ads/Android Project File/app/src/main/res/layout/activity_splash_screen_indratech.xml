<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/cordi"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/primary">


        <ImageView
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_centerInParent="true"
            android:src="@drawable/logo" />


    </RelativeLayout>



    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="40dp"
        android:gravity="center">


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/created_by"
            android:textColor="@color/white"
            android:textSize="@dimen/_15sdp" />

    </RelativeLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
