<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.inapp.vpn">


    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application
        android:name="com.inapp.vpn.App"
        android:allowBackup="true"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:replace="android:networkSecurityConfig"
        android:theme="@style/AppTheme"
        tools:ignore="GoogleAppIndexingWarning">
        <activity android:name="com.inapp.vpn.Activities.UnlockAllActivity" />
        <activity
            android:name="com.inapp.vpn.Activities.ContentsActivity"
            android:label="@string/app_name">
        </activity>
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~3347511713" />

        <activity
            android:name="com.inapp.vpn.Activities.SplashScreen"
            android:theme="@style/Splashscreentheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.inapp.vpn.Activities.Servers"
            android:parentActivityName="com.inapp.vpn.Activities.MainActivity" />
        <activity android:name="com.inapp.vpn.Activities.MainActivity" />
        <activity android:name="com.facebook.ads.AudienceNetworkActivity" android:hardwareAccelerated="true" />

        <activity
            android:name="top.oneconnectapi.app.DisconnectVPNActivity"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:taskAffinity=".DisconnectVPN"
            android:theme="@style/blinkt.dialog" />

        <service
            android:name="top.oneconnectapi.app.core.OpenVPNService"
            android:permission="android.permission.BIND_VPN_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>


        <!-- OneSignal -->

        <meta-data
            android:name="onesignal_app_id"
            android:value="${onesignal_app_id}" />
        <!-- Deprecated - Pulled from OneSignal dashboard. -->
        <meta-data
            android:name="onesignal_google_project_number"
            android:value="str:${onesignal_google_project_number}" />

        <receiver
            android:name="com.onesignal.GcmBroadcastReceiver"
            android:permission="com.google.android.c2dm.permission.SEND"
            android:exported="true">

            <!-- High priority so OneSignal payloads can be filtered from other GCM receivers if filterOtherGCMReceivers is enabled. -->
            <intent-filter android:priority="999" >
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.onesignal.NotificationOpenedReceiver" />

        <service
            android:name="com.onesignal.HmsMessageServiceOneSignal"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <activity
            android:name="com.onesignal.NotificationOpenedActivityHMS"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

        <service android:name="com.onesignal.GcmIntentService" />
        <service
            android:name="com.onesignal.GcmIntentJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="com.onesignal.RestoreJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="com.onesignal.RestoreKickoffJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="com.onesignal.SyncService"
            android:stopWithTask="true" />
        <service
            android:name="com.onesignal.SyncJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <activity
            android:name="com.onesignal.PermissionsActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <service android:name="com.onesignal.NotificationRestoreService" />

        <receiver android:name="com.onesignal.BootUpReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.onesignal.UpgradeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
            </intent-filter>
        </receiver>

        <!-- OneSignal -->

    </application>

</manifest>