apply plugin: 'com.onesignal.androidsdk.onesignal-gradle-plugin'

apply plugin: 'com.android.application'
//apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

ext {
    androidSupportLibraryVersion = "28.0.0"
}

android {
    compileSdk 34

    namespace "com.inapp.vpn"

    packagingOptions {
        pickFirst '**/*.so'
    }

    defaultConfig {
        configurations.all {
            resolutionStrategy { force 'androidx.core:core-ktx:1.6.0' }
        }
        applicationId 'com.inapp.vpn'
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 6
        versionName "6"

        manifestPlaceholders = [
                onesignal_app_id: '************************************',
                // Project number pulled from dashboard, local 27640849 value is ignored.
                onesignal_google_project_number: 'REMOTE'
        ]

        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        vectorDrawables.useSupportLibrary = true
        buildFeatures {
            dataBinding = true
        }
    }

    buildTypes {
        release {

            lintOptions {
                disable 'MissingTranslation'
                checkReleaseBuilds false
                abortOnError false
            }

            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            pseudoLocalesEnabled false
            zipAlignEnabled true
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

        }
        debug {
        }
    }
    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    tasks.withType(JavaCompile).configureEach{
        options.fork = true
        options.forkOptions.jvmArgs +=[
                '--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED',
                '--add-exports=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED',
                '--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED']
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.annotation:annotation:1.2.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation 'com.android.volley:volley:1.1.1'
    implementation("com.android.billingclient:billing:6.0.1")
    implementation 'com.google.guava:guava:27.0.1-android'

    def lottieVersion = '3.3.1'
    implementation "com.airbnb.android:lottie:$lottieVersion"

    implementation 'com.cardiomood.android:android-widgets:0.1.1'
    implementation 'com.etebarian:meow-bottom-navigation:1.2.0'
    implementation 'com.facebook.android:audience-network-sdk:6.14.0'
    implementation 'com.google.ads.mediation:facebook:6.14.0.0'
    implementation 'com.github.anastr:speedviewlib:1.3.0'
    implementation 'com.github.bmarrdev:android-DecoView-charting:v1.2'

    def work_version = "2.7.0"

// (Java only)
    implementation "androidx.work:work-runtime:$work_version"

// Kotlin + coroutines
    implementation "androidx.work:work-runtime-ktx:$work_version"

    implementation 'com.github.bumptech.glide:glide:4.10.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.9.0'

    implementation 'com.github.daoibrahim:AdmobAdvancedNativeRecyclerview:1.0.0'
    implementation 'com.github.GrenderG:Toasty:1.2.5'
    implementation 'com.github.javiersantos:MaterialStyledDialogs:2.1'
    implementation 'com.github.oatrice:internet-speed-testing:1.0.1'
    implementation 'com.github.Shashank02051997:FancyAlertDialog-Android:0.1'
    implementation 'com.github.Shashank02051997:FancyGifDialog-Android:1.2'
    implementation 'com.google.android.gms:play-services-ads:22.0.0'
    implementation 'com.google.android.gms:play-services-basement:17.6.0'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'com.google.android.play:core:1.10.0'
    implementation 'com.google.android.play:core-ktx:1.8.1'
    implementation 'com.google.firebase:firebase-analytics:21.2.2'
    implementation 'com.google.firebase:firebase-database:20.2.0'
    implementation 'com.infideap.drawerbehavior:drawer-behavior:1.0.4'
    implementation 'com.intuit.sdp:sdp-android:1.0.4'

    implementation 'com.jakewharton:butterknife:10.1.0'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.1.0'

    implementation 'com.onesignal:OneSignal:3.15.3'
    implementation 'com.pixplicity.easyprefs:library:1.9.0'
    implementation 'com.skyfishjy.ripplebackground:library:1.0.1'
    implementation 'com.yarolegovich:lovely-dialog:1.1.0'
    implementation 'com.zys:brokenview:1.0.3'
    implementation 'devlight.io:navigationtabbar:1.2.5'
    implementation 'fr.bmartel:jspeedtest:1.32.1'
    implementation 'io.github.dreierf:material-intro-screen:0.0.6'
    implementation 'jp.wasabeef:recyclerview-animators:2.2.5'
    implementation 'me.itangqi.waveloadingview:library:0.3.5'
    implementation 'net.cachapa.expandablelayout:expandablelayout:[2.9.2]'
    implementation 'net.grandcentrix.tray:tray:0.12.0'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.+'

    testImplementation 'junit:junit:4.13.2'

    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'

    // OneConnect Implementation
    implementation 'com.github.oneconnectapi:OneConnectLib:v1.1.0'
    // OneConnect Implementation
    implementation 'com.github.gemeauxph:MaterialLoadingIndicator:v1.0.4'
}
