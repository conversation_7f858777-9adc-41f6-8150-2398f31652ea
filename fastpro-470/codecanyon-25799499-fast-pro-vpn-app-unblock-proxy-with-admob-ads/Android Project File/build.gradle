buildscript {
    ext.kotlin_version = '1.6.20'

    repositories {
        maven { url 'https://maven.google.com' }
        maven { url 'https://plugins.gradle.org/m2/'}
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:[0.13.4, 0.99.99]'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven { url 'https://maven.google.com' }
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
